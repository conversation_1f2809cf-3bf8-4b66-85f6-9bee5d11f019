import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// PUT - Update a life aspect
export async function PUT(request, { params }) {
  try {
    console.log("🎨 Life aspects PUT API - Getting session...");
    console.log("🎨 Life aspects PUT API - Request headers:", Object.fromEntries(request.headers.entries()));
    console.log("🎨 Life aspects PUT API - Params:", params);

    const session = await getServerSession(authOptions);
    console.log("🎨 Life aspects PUT API - Session:", session);
    console.log("🎨 Life aspects PUT API - Session user ID:", session?.user?.id);

    if (!session?.user?.id) {
      console.log("🎨 Life aspects PUT API - No session or user ID, returning 401");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    const { name, color, sort_order } = await request.json();

    // Verify the life aspect belongs to the user
    const existingAspect = await prisma.workitems_life_aspect.findFirst({
      where: {
        id: id,
        user_id: session.user.id,
      },
    });

    if (!existingAspect) {
      return NextResponse.json({ error: "Life aspect not found" }, { status: 404 });
    }

    // If name is being changed, check for duplicates
    if (name && name !== existingAspect.name) {
      const duplicateAspect = await prisma.workitems_life_aspect.findUnique({
        where: {
          user_id_name: {
            user_id: session.user.id,
            name: name,
          },
        },
      });

      if (duplicateAspect) {
        return NextResponse.json({ error: "Life aspect with this name already exists" }, { status: 400 });
      }
    }

    const updatedAspect = await prisma.workitems_life_aspect.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(color && { color }),
        ...(sort_order !== undefined && { sort_order }),
      },
    });

    return NextResponse.json(updatedAspect);
  } catch (error) {
    console.error("Error updating life aspect:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE - Delete a life aspect
export async function DELETE(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Verify the life aspect belongs to the user
    const existingAspect = await prisma.workitems_life_aspect.findFirst({
      where: {
        id: id,
        user_id: session.user.id,
      },
    });

    if (!existingAspect) {
      return NextResponse.json({ error: "Life aspect not found" }, { status: 404 });
    }

    // Delete the life aspect (cascade will handle related records)
    await prisma.workitems_life_aspect.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Life aspect deleted successfully" });
  } catch (error) {
    console.error("Error deleting life aspect:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
