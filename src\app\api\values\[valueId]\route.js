import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// PUT - Update a value
export async function PUT(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { valueId } = params;
    const { value, color, sort_order, is_default } = await request.json();

    // Verify the value belongs to a property owned by the user
    const existingValue = await prisma.workitems_custom_field_choice_option.findFirst({
      where: {
        id: valueId,
        field_definition: {
          user_id: session.user.id,
        },
      },
      include: {
        field_definition: true,
      },
    });

    if (!existingValue) {
      return NextResponse.json({ error: "Value not found" }, { status: 404 });
    }

    // If value is being changed, check for duplicates within the same property
    if (value && value !== existingValue.value) {
      const duplicateValue = await prisma.workitems_custom_field_choice_option.findUnique({
        where: {
          field_definition_id_value: {
            field_definition_id: existingValue.field_definition_id,
            value: value,
          },
        },
      });

      if (duplicateValue) {
        return NextResponse.json({ error: "Value already exists for this property" }, { status: 400 });
      }
    }

    // Handle default value uniqueness constraint
    let updatedValue;
    if (is_default === true) {
      // Use transaction to ensure only one default value per property
      updatedValue = await prisma.$transaction(async (tx) => {
        // First, set all other options for this property to not be default
        await tx.workitems_custom_field_choice_option.updateMany({
          where: {
            field_definition_id: existingValue.field_definition_id,
            id: { not: valueId },
          },
          data: { is_default: false },
        });

        // Then update the target option
        return await tx.workitems_custom_field_choice_option.update({
          where: { id: valueId },
          data: {
            ...(value && { value }),
            ...(color && { color }),
            ...(sort_order !== undefined && { sort_order }),
            is_default: true,
          },
        });
      });
    } else {
      // Regular update without default value logic
      updatedValue = await prisma.workitems_custom_field_choice_option.update({
        where: { id: valueId },
        data: {
          ...(value && { value }),
          ...(color && { color }),
          ...(sort_order !== undefined && { sort_order }),
          ...(is_default !== undefined && { is_default }),
        },
      });
    }

    return NextResponse.json(updatedValue);
  } catch (error) {
    console.error("Error updating value:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE - Delete a value
export async function DELETE(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { valueId } = params;

    // Verify the value belongs to a property owned by the user
    const existingValue = await prisma.workitems_custom_field_choice_option.findFirst({
      where: {
        id: valueId,
        field_definition: {
          user_id: session.user.id,
        },
      },
    });

    if (!existingValue) {
      return NextResponse.json({ error: "Value not found" }, { status: 404 });
    }

    // Delete the value
    await prisma.workitems_custom_field_choice_option.delete({
      where: { id: valueId },
    });

    return NextResponse.json({ message: "Value deleted successfully" });
  } catch (error) {
    console.error("Error deleting value:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
