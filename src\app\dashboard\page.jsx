"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { ChevronDown, Filter, Plus } from "lucide-react";
import ProjectItem from "@/components/dashboard/ProjectItem";
import EditProjectModal from "@/components/dashboard/EditProjectModal";
import CreateProjectModal from "@/components/dashboard/CreateProjectModal";

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // State for dropdown selections
  const [availableProperties, setAvailableProperties] = useState([]);
  const [selectedRowProperty, setSelectedRowProperty] = useState("lifeAspect");
  const [selectedColumnProperty, setSelectedColumnProperty] = useState("");
  const [matrixData, setMatrixData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [matrixLoading, setMatrixLoading] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingProjectId, setEditingProjectId] = useState(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Fetch available properties for dropdowns
  useEffect(() => {
    const fetchProperties = async () => {
      if (status === "loading") {
        return;
      }

      if (!session?.user?.id) {
        router.push("/auth/login");
        return;
      }

      try {
        const response = await fetch("/api/properties/for-view", {
          credentials: "include",
        });

        if (response.ok) {
          const properties = await response.json();
          setAvailableProperties(properties);

          // Set default column property to first property different from default row property
          const defaultColumnProperty = properties.find((p) => p.id !== selectedRowProperty);
          if (defaultColumnProperty) {
            setSelectedColumnProperty(defaultColumnProperty.id);
          }
        }
      } catch (error) {
        console.error("Error fetching properties:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, [session, status, router]);

  // Fetch matrix data when selections change
  useEffect(() => {
    const fetchMatrixData = async () => {
      if (!selectedRowProperty || !selectedColumnProperty || loading) {
        return;
      }

      setMatrixLoading(true);

      try {
        const requestBody = {
          rowPropertyId: selectedRowProperty,
          columnPropertyId: selectedColumnProperty,
        };

        const response = await fetch("/api/projects/matrix-view", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify(requestBody),
        });

        if (response.ok) {
          const data = await response.json();
          setMatrixData(data);
        } else {
          setMatrixData(null);
        }
      } catch (error) {
        console.error("Error fetching matrix data:", error);
        setMatrixData(null);
      } finally {
        setMatrixLoading(false);
      }
    };

    fetchMatrixData();
  }, [selectedRowProperty, selectedColumnProperty, loading]);

  // Handler for opening edit modal
  const handleEditProject = (projectId) => {
    setEditingProjectId(projectId);
    setIsEditModalOpen(true);
  };

  // Handler for closing edit modal
  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setEditingProjectId(null);
  };

  // Handler for saving project changes
  const handleSaveProject = () => {
    // Refresh matrix data to show updated project
    if (selectedRowProperty && selectedColumnProperty) {
      setMatrixLoading(true);
      // Re-fetch matrix data
      const fetchMatrixData = async () => {
        try {
          const response = await fetch("/api/projects/matrix-view", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify({
              rowPropertyId: selectedRowProperty,
              columnPropertyId: selectedColumnProperty,
            }),
          });

          if (response.ok) {
            const data = await response.json();
            setMatrixData(data);
          }
        } catch (error) {
          console.error("Error refreshing matrix data:", error);
        } finally {
          setMatrixLoading(false);
        }
      };

      fetchMatrixData();
    }
  };

  // Handler for opening create modal
  const handleCreateProject = () => {
    setIsCreateModalOpen(true);
  };

  // Handler for closing create modal
  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
  };

  // Handler for saving new project
  const handleSaveNewProject = () => {
    // Refresh matrix data to show new project
    handleSaveProject();
  };

  // Handle loading and authentication states after all hooks
  if (status === "loading") {
    console.log("🔍 [DASHBOARD DEBUG] Rendering loading state");
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="text-gray-600 dark:text-gray-400">Loading...</div>
      </div>
    );
  }

  if (!session) {
    console.log("🔍 [DASHBOARD DEBUG] No session, redirecting to login");
    router.push("/login");
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="text-gray-600 dark:text-gray-400">Redirecting to login...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold mb-6" style={{ color: "#32302C" }}>
            <span className="dark:text-white">Clarity Dashboard</span>
          </h1>

          {/* Customize View Controls */}
          <div className="flex items-center space-x-4 mb-6">
            {/* Rows Dropdown */}
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Rows</label>
              <div className="relative">
                <select
                  value={selectedRowProperty}
                  onChange={(e) => setSelectedRowProperty(e.target.value)}
                  className="appearance-none bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-8 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={loading}
                >
                  {availableProperties.map((property) => (
                    <option key={property.id} value={property.id}>
                      {property.name}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* Columns Dropdown */}
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Columns</label>
              <div className="relative">
                <select
                  value={selectedColumnProperty}
                  onChange={(e) => setSelectedColumnProperty(e.target.value)}
                  className="appearance-none bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-8 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={loading}
                >
                  {availableProperties.map((property) => (
                    <option key={property.id} value={property.id}>
                      {property.name}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* Filter Button (Placeholder) */}
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">&nbsp;</label>
              <button
                className="flex items-center space-x-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled
              >
                <Filter className="h-4 w-4" />
                <span>Filter</span>
              </button>
            </div>
          </div>
        </div>

        {/* Matrix Content Area */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
          {loading ? (
            <div className="flex items-center justify-center h-96">
              <div className="text-gray-600 dark:text-gray-400">Loading dashboard...</div>
            </div>
          ) : matrixLoading ? (
            <div className="flex items-center justify-center h-96">
              <div className="text-gray-600 dark:text-gray-400">Loading matrix data...</div>
            </div>
          ) : matrixData ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                {/* Table Header */}
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    {/* Empty cell for row headers */}
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-48 sticky left-0 bg-gray-50 dark:bg-gray-700 z-10">
                      <div className="flex items-center justify-between">
                        <span>{availableProperties.find((p) => p.id === selectedRowProperty)?.name || "Rows"}</span>
                        <button
                          onClick={handleCreateProject}
                          className="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
                          title="Add New Project"
                        >
                          <Plus className="h-4 w-4" />
                        </button>
                      </div>
                    </th>
                    {/* Column headers */}
                    {matrixData.columns.map((column) => (
                      <th
                        key={column.id}
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-64"
                      >
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 rounded-full" style={{ backgroundColor: column.color }} />
                          <span className="truncate">{column.name}</span>
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>

                {/* Table Body */}
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {matrixData.rows.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      {/* Row header */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 sticky left-0 z-10">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 rounded-full" style={{ backgroundColor: row.color }} />
                          <span className="truncate">{row.name}</span>
                        </div>
                      </td>

                      {/* Matrix cells */}
                      {matrixData.columns.map((column) => (
                        <td key={`${row.id}-${column.id}`} className="px-4 py-4 text-sm text-gray-900 dark:text-white align-top">
                          <div className="min-h-24">
                            {/* Render projects using ProjectItem component */}
                            {matrixData.matrixData[row.id] && matrixData.matrixData[row.id][column.id] ? (
                              <div className="space-y-1">
                                {matrixData.matrixData[row.id][column.id].map((projectId) => (
                                  <ProjectItem
                                    key={projectId}
                                    projectId={projectId}
                                    projectMap={matrixData.projectMap}
                                    level={0}
                                    onEditProject={handleEditProject}
                                  />
                                ))}
                              </div>
                            ) : (
                              <div className="text-gray-400 text-xs italic">No projects</div>
                            )}
                          </div>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="flex items-center justify-center h-96">
              <div className="text-gray-600 dark:text-gray-400">No data available</div>
            </div>
          )}
        </div>
      </main>

      {/* Edit Project Modal */}
      <EditProjectModal isOpen={isEditModalOpen} onClose={handleCloseEditModal} projectId={editingProjectId} onSave={handleSaveProject} />

      {/* Create Project Modal */}
      <CreateProjectModal isOpen={isCreateModalOpen} onClose={handleCloseCreateModal} onSave={handleSaveNewProject} />
    </div>
  );
}
