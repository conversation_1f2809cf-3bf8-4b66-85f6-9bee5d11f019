"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter, usePathname } from "next/navigation";
import { signOut } from "next-auth/react";
import { useTheme } from "next-themes";
import { Settings, Sun, Moon, User, LogOut, Database } from "lucide-react";
import Link from "next/link";

export default function Header() {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();
  const dropdownRef = useRef(null);
  const router = useRouter();
  const pathname = usePathname();

  // Ensure component is mounted before rendering theme-dependent content
  useEffect(() => {
    setMounted(true);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleTheme = () => {
    console.log("🎨 [THEME DEBUG] Current theme:", theme);
    console.log("🎨 [THEME DEBUG] Document class list:", document.documentElement.classList.toString());
    const newTheme = theme === "light" ? "dark" : "light";
    console.log("🎨 [THEME DEBUG] Setting new theme:", newTheme);
    setTheme(newTheme);

    // Add a small delay to check if the class was applied
    setTimeout(() => {
      console.log("🎨 [THEME DEBUG] After toggle - Document class list:", document.documentElement.classList.toString());
    }, 100);

    setIsDropdownOpen(false);
  };

  const handleLogout = async () => {
    setIsDropdownOpen(false);
    await signOut({ callbackUrl: "/login" });
  };

  const getNavigationLink = () => {
    if (pathname === "/settings/properties") {
      return { href: "/", label: "Dashboard" };
    }
    return { href: "/settings/properties", label: "Custom Values Settings" };
  };

  const navLink = getNavigationLink();

  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-end items-center h-16">
          <div className="relative">
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Settings"
            >
              <Settings size={20} />
            </button>

            {/* Settings Dropdown */}
            {isDropdownOpen && (
              <div
                ref={dropdownRef}
                className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50"
              >
                <div className="py-1">
                  {/* Navigation Link */}
                  <Link
                    href={navLink.href}
                    onClick={() => setIsDropdownOpen(false)}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <Database size={16} className="mr-3" />
                    {navLink.label}
                  </Link>

                  {/* Theme Toggle */}
                  <button
                    onClick={toggleTheme}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    {mounted ? (
                      theme === "light" ? (
                        <Moon size={16} className="mr-3" />
                      ) : (
                        <Sun size={16} className="mr-3" />
                      )
                    ) : (
                      <div className="w-4 h-4 mr-3" /> // Placeholder to prevent layout shift
                    )}
                    Toggle Theme
                  </button>

                  {/* Profile Management */}
                  <Link
                    href="/profile"
                    onClick={() => setIsDropdownOpen(false)}
                    className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <User size={16} className="mr-3" />
                    Profile Management
                  </Link>

                  {/* Divider */}
                  <div className="border-t border-gray-200 dark:border-gray-600 my-1"></div>

                  {/* Logout */}
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    <LogOut size={16} className="mr-3" />
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
