import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";

// PUT - Update a life aspect
export async function PUT(request, { params }) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    const { name, color, sort_order } = await request.json();

    // Verify the life aspect belongs to the user
    const existingAspect = await prisma.workitems_life_aspect.findFirst({
      where: {
        id: id,
        user_id: session.user.id,
      },
    });

    if (!existingAspect) {
      return NextResponse.json({ error: "Life aspect not found" }, { status: 404 });
    }

    // Check if another aspect with the same name exists (excluding current aspect)
    if (name && name !== existingAspect.name) {
      const duplicateAspect = await prisma.workitems_life_aspect.findFirst({
        where: {
          user_id: session.user.id,
          name: name,
          id: { not: id },
        },
      });

      if (duplicateAspect) {
        return NextResponse.json({ error: "Life aspect with this name already exists" }, { status: 400 });
      }
    }

    const updatedAspect = await prisma.workitems_life_aspect.update({
      where: { id: id },
      data: {
        ...(name && { name }),
        ...(color && { color }),
        ...(sort_order !== undefined && { sort_order }),
      },
    });

    return NextResponse.json(updatedAspect);
  } catch (error) {
    console.error("Error updating life aspect:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE - Delete a life aspect
export async function DELETE(request, { params }) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Verify the life aspect belongs to the user
    const existingAspect = await prisma.workitems_life_aspect.findFirst({
      where: {
        id: id,
        user_id: session.user.id,
      },
    });

    if (!existingAspect) {
      return NextResponse.json({ error: "Life aspect not found" }, { status: 404 });
    }

    // Delete the life aspect
    await prisma.workitems_life_aspect.delete({
      where: { id: id },
    });

    return NextResponse.json({ message: "Life aspect deleted successfully" });
  } catch (error) {
    console.error("Error deleting life aspect:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
