# Sankalp Application: Style Guide

This document outlines the design principles, typography, color palette, and component styles for the Clarity application. The goal is to maintain a consistent, clean, and functional user interface across the entire product.

---

## 1. Core Design Philosophy

The application's design is guided by three core principles:

- **Minimalist**: The UI must be clean, uncluttered, and free of unnecessary visual noise. Every element should serve a clear purpose.
- **Functional**: The design should be intuitive and make it easy for users to accomplish their tasks efficiently.
- **Focused**: The aesthetic should feel like a calm, digital "A4 paper," helping users concentrate on their content and planning.

---

## 2. Typography

- **Font Family**: `Inter` (or `ui-sans-serif` as a system fallback)
- **Default Text Color (Light Theme)**: `#32302C`
- **Default Text Color (Dark Theme)**: `#FFFFFFCF` (slightly off-white)

### Typographic Scale

| Element           | Font Weight              | Size (Tailwind) | Notes                                      |
|-------------------|---------------------------|------------------|---------------------------------------------|
| Page Title        | `font-serif font-bold`    | `text-2xl`       | For main section titles like "This Week's Outcomes". |
| Heading 1 (H1)    | `font-semibold`           | `text-xl`        | For primary headings like the Week Navigator. |
| Heading 2 (H2)    | `font-semibold`           | `text-lg`        | For sub-headings or modal titles.          |
| Body Text         | `font-normal`             | `text-base`      | Default text for paragraphs and descriptions. |
| Labels / Small    | `font-medium`             | `text-sm`        | For form labels and secondary information. |
| Caption / X-Small | `font-normal`             | `text-xs`        | For tertiary details and hints.            |

---

## 3. Color Palette

The application uses a dual-theme system. All colors are specified to create a consistent experience in both Light and Dark modes.

### A. Light Theme Palette

- **Default Background**: `rgb(255, 255, 255)`
- **UI Background (Subtle)**: `#F9FAFB`
- **Default Text**: `#32302C`

#### Tag & Option Colors (Light Theme)

| Color Name | Text Color (rgb) | Background Color (rgb) |
|------------|------------------|-------------------------|
| Gray       | 115, 114, 110    | 248, 248, 247           |
| Brown      | 159, 107, 83     | 244, 238, 238           |
| Orange     | 217, 115, 13     | 251, 236, 221           |
| Yellow     | 203, 145, 47     | 251, 243, 219           |
| Green      | 68, 131, 97      | 237, 243, 236           |
| Blue       | 51, 126, 169     | 231, 243, 248           |
| Purple     | 144, 101, 176    | 248, 243, 252           |
| Pink       | 193, 76, 138     | 252, 241, 246           |
| Red        | 205, 60, 58      | 253, 235, 236           |

### B. Dark Theme Palette

- **Default Background**: `rgb(25, 25, 25)`
- **UI Background (Subtle)**: `#1F1F1F`
- **Default Text**: `#FFFFFFCF`

#### Tag & Option Colors (Dark Theme)

| Color Name | Text Color (rgb) | Background Color (rgb) |
|------------|------------------|-------------------------|
| Gray       | 155, 155, 155    | 47, 47, 47              |
| Brown      | 186, 133, 111    | 74, 50, 40              |
| Orange     | 199, 125, 72     | 92, 59, 35              |
| Yellow     | 202, 152, 77     | 86, 67, 40              |
| Green      | 82, 158, 114     | 36, 61, 48              |
| Blue       | 55, 154, 211     | 20, 58, 78              |
| Purple     | 157, 104, 211    | 60, 45, 73              |
| Pink       | 209, 87, 150     | 78, 44, 60              |
| Red        | 230, 91, 88      | 82, 46, 42              |

---

## 4. Components

- **Buttons**: Use soft, rounded corners (`rounded-lg`). Primary actions should use an accent color, while secondary actions can be more subtle (e.g., light gray background).
- **Inputs & Dropdowns**: Use a simple, light border in Light Mode and a slightly lighter background in Dark Mode to define their shape. Maintain consistent padding.
- **Cards & Modals**: Use generous padding and soft shadows (`shadow-md` or `shadow-lg`) to create a sense of depth. Corners should be well-rounded (`rounded-xl` or `rounded-2xl`).

---

## 5. Iconography

- **Library**: Use a clean, modern, and consistent icon set. `Lucide-React` is the recommended library.
- **Style**: Icons should be simple, line-based, and have a consistent stroke width.
