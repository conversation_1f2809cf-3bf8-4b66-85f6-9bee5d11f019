import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - Fetch all properties for the user
export async function GET(request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const properties = await prisma.workitems_custom_field_definition.findMany({
      where: {
        user_id: session.user.id,
      },
      orderBy: { sort_order: "asc" },
    });

    return NextResponse.json(properties);
  } catch (error) {
    console.error("Error fetching properties:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST - Create a new property
export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name, field_type, is_required, sort_order } = await request.json();

    if (!name) {
      return NextResponse.json({ error: "Name is required" }, { status: 400 });
    }

    // Check if property with same name already exists for this user
    const existingProperty = await prisma.workitems_custom_field_definition.findUnique({
      where: {
        user_id_name: {
          user_id: session.user.id,
          name: name,
        },
      },
    });

    if (existingProperty) {
      return NextResponse.json({ error: "Property with this name already exists" }, { status: 400 });
    }

    // Get the next sort order if not provided
    let nextSortOrder = sort_order;
    if (nextSortOrder === undefined) {
      const lastProperty = await prisma.workitems_custom_field_definition.findFirst({
        where: { user_id: session.user.id },
        orderBy: { sort_order: "desc" },
      });
      nextSortOrder = (lastProperty?.sort_order || 0) + 1;
    }

    const property = await prisma.workitems_custom_field_definition.create({
      data: {
        user_id: session.user.id,
        name,
        field_type: field_type || "Select",
        is_required: is_required || false,
        sort_order: nextSortOrder,
      },
    });

    return NextResponse.json(property, { status: 201 });
  } catch (error) {
    console.error("Error creating property:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
