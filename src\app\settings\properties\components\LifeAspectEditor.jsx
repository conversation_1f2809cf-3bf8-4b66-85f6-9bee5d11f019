"use client";

import { useState, useEffect } from "react";
import { Trash2 } from "lucide-react";
import ColorPicker from "./ColorPicker";

export default function LifeAspectEditor({ aspect, onUpdate, onDelete }) {
  const [name, setName] = useState("");
  const [selectedColor, setSelectedColor] = useState("blue");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (aspect) {
      setName(aspect.name);
      setSelectedColor(aspect.color || "blue");
    }
  }, [aspect]);

  const handleNameChange = async (newName) => {
    if (newName.trim() !== aspect.name && aspect) {
      try {
        const response = await fetch(`/api/life-aspects/${aspect.id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ name: newName.trim() }),
          credentials: "include",
        });

        if (response.ok) {
          const updatedAspect = await response.json();
          onUpdate(updatedAspect);
        }
      } catch (error) {
        console.error("Error updating aspect name:", error);
      }
    }
  };

  const handleColorChange = async (newColor) => {
    if (newColor !== aspect.color && aspect) {
      try {
        const response = await fetch(`/api/life-aspects/${aspect.id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ color: newColor }),
          credentials: "include",
        });

        if (response.ok) {
          const updatedAspect = await response.json();
          onUpdate(updatedAspect);
          setSelectedColor(newColor);
        }
      } catch (error) {
        console.error("Error updating aspect color:", error);
      }
    }
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/life-aspects/${aspect.id}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (response.ok) {
        onDelete(aspect.id);
      }
    } catch (error) {
      console.error("Error deleting life aspect:", error);
    }
    setShowDeleteConfirm(false);
  };

  if (!aspect) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400 mt-20">
        <p>Select a life aspect to edit</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Life Aspect Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name</label>
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          onBlur={() => handleNameChange(name)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter life aspect name"
        />
      </div>

      {/* Color Picker */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Color</label>
        <ColorPicker selectedColor={selectedColor} onColorChange={handleColorChange} />
      </div>

      {/* Delete Life Aspect */}
      <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setShowDeleteConfirm(true)}
          className="flex items-center space-x-2 px-4 py-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md"
        >
          <Trash2 size={16} />
          <span>Delete</span>
        </button>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Delete Life Aspect</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Are you sure you want to delete "{aspect.name}"? This action cannot be undone and will affect any projects associated with this life
              aspect.
            </p>
            <div className="flex space-x-3">
              <button onClick={handleDelete} className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                Delete
              </button>
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
