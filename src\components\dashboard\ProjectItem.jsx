"use client";

import { ChevronRight, ChevronDown } from "lucide-react";
import { useState } from "react";
import ContextMenu from "./ContextMenu";

export default function ProjectItem({ projectId, projectMap, level = 0, onEditProject }) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [contextMenu, setContextMenu] = useState(null);

  // Get project data from the projectMap
  const project = projectMap[projectId];

  if (!project) {
    return null;
  }

  const hasSubProjects = project.subProjectIds && project.subProjectIds.length > 0;
  const indentLevel = level * 16; // 16px per level

  const handleContextMenu = (event) => {
    event.preventDefault();
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
    });
  };

  const handleCloseContextMenu = () => {
    setContextMenu(null);
  };

  return (
    <div className="w-full">
      {/* Main project item */}
      <div
        className="flex items-center space-x-2 py-1 hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-xs"
        style={{ paddingLeft: `${indentLevel}px` }}
        onContextMenu={handleContextMenu}
      >
        {/* Expand/Collapse button for projects with sub-projects */}
        {hasSubProjects ? (
          <button onClick={() => setIsExpanded(!isExpanded)} className="flex-shrink-0 p-0.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded">
            {isExpanded ? <ChevronDown className="h-3 w-3 text-gray-500" /> : <ChevronRight className="h-3 w-3 text-gray-500" />}
          </button>
        ) : (
          <div className="w-4 h-4 flex-shrink-0" /> // Spacer for alignment
        )}

        {/* Project color indicator */}
        {project.life_aspect?.color && <div className="w-2 h-2 rounded-full flex-shrink-0" style={{ backgroundColor: project.life_aspect.color }} />}

        {/* Project name */}
        <div className="flex-1 min-w-0">
          <div className="truncate font-medium text-sm" style={{ color: "#32302C" }}>
            <span className="dark:text-white">{project.name}</span>
          </div>
          {project.description && (
            <div className="truncate text-xs" style={{ color: "#6B7280" }}>
              <span className="dark:text-gray-400">{project.description}</span>
            </div>
          )}
        </div>
      </div>

      {/* Sub-projects (recursive) */}
      {hasSubProjects && isExpanded && (
        <div className="mt-1">
          {project.subProjectIds.map((subProjectId) => (
            <ProjectItem key={subProjectId} projectId={subProjectId} projectMap={projectMap} level={level + 1} onEditProject={onEditProject} />
          ))}
        </div>
      )}

      {/* Context Menu */}
      {contextMenu && (
        <ContextMenu x={contextMenu.x} y={contextMenu.y} projectId={projectId} onClose={handleCloseContextMenu} onEditProject={onEditProject} />
      )}
    </div>
  );
}
