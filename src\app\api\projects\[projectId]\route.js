import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - Fetch a specific project with all its data
export async function GET(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { projectId } = params;
    const userId = session.user.id;

    // Fetch the project with all related data
    const project = await prisma.workitems_project.findFirst({
      where: {
        id: projectId,
        user_id: userId,
      },
      include: {
        life_aspect: true,
        custom_field_values: {
          include: {
            field_definition: true,
            selected_options: true,
          },
        },
        parent_project: {
          select: {
            id: true,
            name: true,
          },
        },
        sub_projects: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Transform custom field values for easier frontend consumption
    const customFieldValues = {};
    project.custom_field_values.forEach((cv) => {
      customFieldValues[cv.field_definition.id] = {
        fieldName: cv.field_definition.name,
        fieldType: cv.field_definition.field_type,
        text_value: cv.text_value,
        selectedOptions: cv.selected_options.map((option) => ({
          id: option.id,
          value: option.value,
          color: option.color,
        })),
      };
    });

    // Prepare response data
    const responseData = {
      id: project.id,
      name: project.name,
      description: project.description,
      life_aspect_id: project.life_aspect_id,
      life_aspect: project.life_aspect ? {
        id: project.life_aspect.id,
        name: project.life_aspect.name,
        color: project.life_aspect.color,
      } : null,
      parent_project_id: project.parent_project_id,
      parent_project: project.parent_project,
      sub_projects: project.sub_projects,
      sort_order: project.sort_order,
      created_at: project.created_at,
      updated_at: project.updated_at,
      customFieldValues,
    };

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error fetching project:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// PUT - Update a project
export async function PUT(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { projectId } = params;
    const userId = session.user.id;
    const { name, description, life_aspect_id, customFields } = await request.json();

    // Verify the project belongs to the user
    const existingProject = await prisma.workitems_project.findFirst({
      where: {
        id: projectId,
        user_id: userId,
      },
    });

    if (!existingProject) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 });
    }

    // Start a transaction to update project and custom fields
    const result = await prisma.$transaction(async (tx) => {
      // Update basic project fields
      const updatedProject = await tx.workitems_project.update({
        where: { id: projectId },
        data: {
          name: name || existingProject.name,
          description: description !== undefined ? description : existingProject.description,
          life_aspect_id: life_aspect_id !== undefined ? (life_aspect_id || null) : existingProject.life_aspect_id,
        },
      });

      // Update custom field values if provided
      if (customFields) {
        for (const [fieldId, fieldData] of Object.entries(customFields)) {
          // Check if a custom field value already exists
          const existingValue = await tx.workitems_custom_field_value.findFirst({
            where: {
              field_definition_id: fieldId,
              project_id: projectId,
            },
          });

          if (fieldData.text_value !== undefined || fieldData.selectedOptions) {
            if (existingValue) {
              // Update existing value
              await tx.workitems_custom_field_value.update({
                where: { id: existingValue.id },
                data: {
                  text_value: fieldData.text_value || null,
                },
              });

              // Handle selected options for Select/MultiSelect fields
              if (fieldData.selectedOptions) {
                // Clear existing selections
                await tx.workitems_custom_field_value.update({
                  where: { id: existingValue.id },
                  data: {
                    selected_options: {
                      set: [], // Clear all existing connections
                    },
                  },
                });

                // Add new selections
                if (fieldData.selectedOptions.length > 0) {
                  await tx.workitems_custom_field_value.update({
                    where: { id: existingValue.id },
                    data: {
                      selected_options: {
                        connect: fieldData.selectedOptions.map((option) => ({ id: option.id })),
                      },
                    },
                  });
                }
              }
            } else {
              // Create new value
              const newValue = await tx.workitems_custom_field_value.create({
                data: {
                  field_definition_id: fieldId,
                  project_id: projectId,
                  text_value: fieldData.text_value || null,
                },
              });

              // Handle selected options for new value
              if (fieldData.selectedOptions && fieldData.selectedOptions.length > 0) {
                await tx.workitems_custom_field_value.update({
                  where: { id: newValue.id },
                  data: {
                    selected_options: {
                      connect: fieldData.selectedOptions.map((option) => ({ id: option.id })),
                    },
                  },
                });
              }
            }
          }
        }
      }

      return updatedProject;
    });

    return NextResponse.json({ success: true, project: result });
  } catch (error) {
    console.error("Error updating project:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
