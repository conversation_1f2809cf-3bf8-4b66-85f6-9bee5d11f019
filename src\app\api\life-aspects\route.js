import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - Fetch all life aspects for the user
export async function GET(request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const lifeAspects = await prisma.workitems_life_aspect.findMany({
      where: {
        user_id: session.user.id,
      },
      orderBy: { sort_order: "asc" },
    });

    return NextResponse.json(lifeAspects);
  } catch (error) {
    console.error("Error fetching life aspects:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST - Create a new life aspect
export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name, color, sort_order } = await request.json();

    if (!name) {
      return NextResponse.json({ error: "Name is required" }, { status: 400 });
    }

    // Check if life aspect with same name already exists for this user
    const existingAspect = await prisma.workitems_life_aspect.findUnique({
      where: {
        user_id_name: {
          user_id: session.user.id,
          name: name,
        },
      },
    });

    if (existingAspect) {
      return NextResponse.json({ error: "Life aspect with this name already exists" }, { status: 400 });
    }

    // Get the next sort order if not provided
    let nextSortOrder = sort_order;
    if (nextSortOrder === undefined) {
      const lastAspect = await prisma.workitems_life_aspect.findFirst({
        where: { user_id: session.user.id },
        orderBy: { sort_order: "desc" },
      });
      nextSortOrder = (lastAspect?.sort_order || 0) + 1;
    }

    const lifeAspect = await prisma.workitems_life_aspect.create({
      data: {
        user_id: session.user.id,
        name,
        color: color || "#3498db",
        sort_order: nextSortOrder,
      },
    });

    return NextResponse.json(lifeAspect, { status: 201 });
  } catch (error) {
    console.error("Error creating life aspect:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
