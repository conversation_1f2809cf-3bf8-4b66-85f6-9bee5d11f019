import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { prisma } from '@/lib/prisma'

// PUT - Reorder life aspects
export async function PUT(request) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { ids } = await request.json()

    if (!Array.isArray(ids)) {
      return NextResponse.json({ error: 'IDs must be an array' }, { status: 400 })
    }

    // Verify all life aspects belong to the user
    const lifeAspects = await prisma.workitems_life_aspect.findMany({
      where: {
        id: { in: ids },
        user_id: session.user.id
      }
    })

    if (lifeAspects.length !== ids.length) {
      return NextResponse.json({ error: 'Some life aspects not found or unauthorized' }, { status: 404 })
    }

    // Update sort orders
    const updatePromises = ids.map((id, index) =>
      prisma.workitems_life_aspect.update({
        where: { id },
        data: { sort_order: index }
      })
    )

    await Promise.all(updatePromises)

    return NextResponse.json({ message: 'Life aspects reordered successfully' })
  } catch (error) {
    console.error('Error reordering life aspects:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
