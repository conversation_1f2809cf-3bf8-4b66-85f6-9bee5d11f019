<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>Clarity Settings</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet" />
    <style>
      body {
        font-family: "Inter", sans-serif;
      }
      .light-theme {
        --bg-default: rgb(255, 255, 255);
        --text-default: #32302c;
        --bg-sidebar: rgb(248, 248, 247);
        --border-default: rgb(230, 230, 229);
        --icon-color: #5a5a5a;
        --input-bg: rgb(248, 248, 247);
        --input-border: rgb(223, 223, 221);
        --input-focus-border: #3b82f6;
        --button-hover-bg: rgb(240, 240, 239);
        --tag-gray-text: rgb(115, 114, 110);
        --tag-gray-bg: rgb(248, 248, 247);
        --tag-brown-text: rgb(159, 107, 83);
        --tag-brown-bg: rgb(244, 238, 238);
        --tag-orange-text: rgb(217, 115, 13);
        --tag-orange-bg: rgb(251, 236, 221);
        --tag-yellow-text: rgb(203, 145, 47);
        --tag-yellow-bg: rgb(251, 243, 219);
        --tag-green-text: rgb(68, 131, 97);
        --tag-green-bg: rgb(237, 243, 236);
        --tag-blue-text: rgb(51, 126, 169);
        --tag-blue-bg: rgb(231, 243, 248);
        --tag-purple-text: rgb(144, 101, 176);
        --tag-purple-bg: rgb(248, 243, 252);
        --tag-pink-text: rgb(193, 76, 138);
        --tag-pink-bg: rgb(252, 241, 246);
        --tag-red-text: rgb(205, 60, 58);
        --tag-red-bg: rgb(253, 235, 236);
      }
      .dark-theme {
        --bg-default: rgb(25, 25, 25);
        --text-default: #ffffffcf;
        --bg-sidebar: rgb(35, 35, 35);
        --border-default: rgb(50, 50, 50);
        --icon-color: #a0a0a0;
        --input-bg: rgb(40, 40, 40);
        --input-border: rgb(60, 60, 60);
        --input-focus-border: #3b82f6;
        --button-hover-bg: rgb(45, 45, 45);
        --tag-gray-text: rgb(155, 155, 155);
        --tag-gray-bg: rgb(47, 47, 47);
        --tag-brown-text: rgb(186, 133, 111);
        --tag-brown-bg: rgb(74, 50, 40);
        --tag-orange-text: rgb(199, 125, 72);
        --tag-orange-bg: rgb(92, 59, 35);
        --tag-yellow-text: rgb(202, 152, 77);
        --tag-yellow-bg: rgb(86, 67, 40);
        --tag-green-text: rgb(82, 158, 114);
        --tag-green-bg: rgb(36, 61, 48);
        --tag-blue-text: rgb(55, 154, 211);
        --tag-blue-bg: rgb(20, 58, 78);
        --tag-purple-text: rgb(157, 104, 211);
        --tag-purple-bg: rgb(60, 45, 73);
        --tag-pink-text: rgb(209, 87, 150);
        --tag-pink-bg: rgb(78, 44, 60);
        --tag-red-text: rgb(230, 91, 88);
        --tag-red-bg: rgb(82, 46, 42);
      }
      .draggable-item {
        cursor: grab;
      }
      .draggable-item:active {
        cursor: grabbing;
      }
      .color-picker-dot {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 8px;
        border: 1px solid var(--border-default);
      }
      .color-picker-dot.selected {
        box-shadow: 0 0 0 2px var(--input-focus-border);
      }
    </style>
  </head>
  <body class="light-theme bg-[var(--bg-default)] text-[var(--text-default)] flex h-screen overflow-hidden">
    <aside class="w-72 bg-[var(--bg-sidebar)] p-6 border-r border-[var(--border-default)] flex flex-col space-y-6">
      <div>
        <h2 class="text-sm font-semibold text-[var(--text-default)] opacity-70 mb-2 px-2">Custom Fields</h2>
        <nav class="space-y-1">
          <a
            class="flex items-center px-2 py-1.5 text-[var(--text-default)] rounded-md hover:bg-[var(--button-hover-bg)] font-medium bg-[var(--button-hover-bg)]"
            href="#"
            id="nav-priority"
          >
            Priority
          </a>
          <a class="flex items-center px-2 py-1.5 text-[var(--text-default)] rounded-md hover:bg-[var(--button-hover-bg)]" href="#" id="nav-status">
            Status
          </a>
          <a class="flex items-center px-2 py-1.5 text-[var(--text-default)] rounded-md hover:bg-[var(--button-hover-bg)]" href="#" id="nav-project">
            Project
          </a>
        </nav>
      </div>
      <div>
        <h2 class="text-sm font-semibold text-[var(--text-default)] opacity-70 mb-2 px-2">Life Aspects</h2>
        <nav class="space-y-1">
          <a class="flex items-center px-2 py-1.5 text-[var(--text-default)] rounded-md hover:bg-[var(--button-hover-bg)]" href="#" id="nav-work">
            Work
          </a>
          <a class="flex items-center px-2 py-1.5 text-[var(--text-default)] rounded-md hover:bg-[var(--button-hover-bg)]" href="#" id="nav-personal">
            Personal
          </a>
          <a class="flex items-center px-2 py-1.5 text-[var(--text-default)] rounded-md hover:bg-[var(--button-hover-bg)]" href="#" id="nav-learning">
            Learning
          </a>
        </nav>
      </div>
      <div class="mt-auto">
        <button
          class="w-full flex items-center justify-center px-3 py-2 text-sm text-[var(--text-default)] rounded-md hover:bg-[var(--button-hover-bg)] border border-[var(--border-default)]"
          id="theme-toggle"
        >
          <span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)]">brightness_6</span>
          Toggle Theme
        </button>
      </div>
    </aside>
    <main class="flex-1 p-8 overflow-y-auto">
      <div class="max-w-2xl mx-auto">
        <div id="editor-priority">
          <div class="mb-6">
            <label class="block text-sm font-medium text-[var(--text-default)] mb-1" for="priority-name">Name</label>
            <input
              class="w-full px-3 py-2 text-[var(--text-default)] bg-[var(--input-bg)] border border-[var(--input-border)] rounded-md focus:ring-1 focus:ring-[var(--input-focus-border)] focus:border-[var(--input-focus-border)] outline-none"
              id="priority-name"
              type="text"
              value="Priority"
            />
          </div>
          <div class="mb-6 relative">
            <label class="block text-sm font-medium text-[var(--text-default)] mb-1" for="priority-type">Type</label>
            <div class="relative">
              <button
                class="w-full flex items-center justify-between px-3 py-2 text-[var(--text-default)] bg-[var(--input-bg)] border border-[var(--input-border)] rounded-md focus:ring-1 focus:ring-[var(--input-focus-border)] focus:border-[var(--input-focus-border)] outline-none text-left"
                id="priority-type-button"
              >
                <span>Select</span>
                <span class="material-icons-outlined text-lg text-[var(--icon-color)]">unfold_more</span>
              </button>
              <div
                class="absolute z-10 mt-1 w-full bg-[var(--bg-default)] border border-[var(--border-default)] rounded-md shadow-lg hidden"
                id="priority-type-dropdown"
              >
                <a class="flex items-center px-3 py-2 hover:bg-[var(--button-hover-bg)]" data-value="Text" href="#">
                  <span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)]">title</span> Text
                </a>
                <a class="flex items-center px-3 py-2 hover:bg-[var(--button-hover-bg)]" data-value="Number" href="#">
                  <span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)]">pin</span> Number
                </a>
                <a
                  class="flex items-center justify-between px-3 py-2 hover:bg-[var(--button-hover-bg)] bg-[var(--button-hover-bg)]"
                  data-value="Select"
                  href="#"
                >
                  <div class="flex items-center"><span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)]">list</span> Select</div>
                  <span class="material-icons-outlined text-lg text-blue-500">check</span>
                </a>
                <a class="flex items-center px-3 py-2 hover:bg-[var(--button-hover-bg)]" data-value="Multi-select" href="#">
                  <span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)]">checklist</span> Multi-select
                </a>
                <a class="flex items-center px-3 py-2 hover:bg-[var(--button-hover-bg)]" data-value="Status" href="#">
                  <span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)]">hourglass_empty</span> Status
                </a>
                <a class="flex items-center px-3 py-2 hover:bg-[var(--button-hover-bg)]" data-value="Date" href="#">
                  <span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)]">calendar_today</span> Date
                </a>
                <a class="flex items-center px-3 py-2 hover:bg-[var(--button-hover-bg)]" data-value="Person" href="#">
                  <span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)]">person_outline</span> Person
                </a>
              </div>
            </div>
          </div>
          <div class="mb-6">
            <label class="block text-sm font-medium text-[var(--text-default)] mb-1">Options</label>
            <div class="space-y-2" id="priority-options-list">
              <div class="flex items-center space-x-2 p-2 border border-[var(--border-default)] rounded-md bg-[var(--bg-default)] draggable-item">
                <span class="material-icons-outlined text-lg text-[var(--icon-color)] cursor-grab">drag_indicator</span>
                <div class="w-5 h-5 rounded-full" style="background-color: rgb(253, 235, 236); border: 1px solid rgb(205, 60, 58)"></div>
                <input
                  class="flex-1 px-2 py-1 text-sm text-[var(--text-default)] bg-transparent border-none focus:ring-0 outline-none"
                  type="text"
                  value="1. Urgent and Uplifting"
                />
                <button class="text-[var(--icon-color)] hover:text-red-500">
                  <span class="material-icons-outlined text-lg">delete</span>
                </button>
              </div>
              <div class="flex items-center space-x-2 p-2 border border-[var(--border-default)] rounded-md bg-[var(--bg-default)] draggable-item">
                <span class="material-icons-outlined text-lg text-[var(--icon-color)] cursor-grab">drag_indicator</span>
                <div class="w-5 h-5 rounded-full" style="background-color: rgb(252, 241, 246); border: 1px solid rgb(193, 76, 138)"></div>
                <input
                  class="flex-1 px-2 py-1 text-sm text-[var(--text-default)] bg-transparent border-none focus:ring-0 outline-none"
                  type="text"
                  value="2. Urgent and not Uplifting"
                />
                <button class="text-[var(--icon-color)] hover:text-red-500">
                  <span class="material-icons-outlined text-lg">delete</span>
                </button>
              </div>
              <div class="flex items-center space-x-2 p-2 border border-[var(--border-default)] rounded-md bg-[var(--bg-default)] draggable-item">
                <span class="material-icons-outlined text-lg text-[var(--icon-color)] cursor-grab">drag_indicator</span>
                <div class="w-5 h-5 rounded-full" style="background-color: rgb(237, 243, 236); border: 1px solid rgb(68, 131, 97)"></div>
                <input
                  class="flex-1 px-2 py-1 text-sm text-[var(--text-default)] bg-transparent border-none focus:ring-0 outline-none"
                  type="text"
                  value="3. Not Urgent and Uplifting"
                />
                <button class="text-[var(--icon-color)] hover:text-red-500">
                  <span class="material-icons-outlined text-lg">delete</span>
                </button>
              </div>
              <div class="flex items-center space-x-2 p-2 border border-[var(--border-default)] rounded-md bg-[var(--bg-default)] draggable-item">
                <span class="material-icons-outlined text-lg text-[var(--icon-color)] cursor-grab">drag_indicator</span>
                <div class="w-5 h-5 rounded-full" style="background-color: rgb(248, 248, 247); border: 1px solid rgb(115, 114, 110)"></div>
                <input
                  class="flex-1 px-2 py-1 text-sm text-[var(--text-default)] bg-transparent border-none focus:ring-0 outline-none"
                  type="text"
                  value="4. Not Urgent and not Uplifting"
                />
                <button class="text-[var(--icon-color)] hover:text-red-500">
                  <span class="material-icons-outlined text-lg">delete</span>
                </button>
              </div>
            </div>
            <button class="mt-3 flex items-center text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
              <span class="material-icons-outlined text-lg mr-1">add_circle_outline</span> Add an option
            </button>
          </div>
          <div class="mb-6 relative">
            <label class="block text-sm font-medium text-[var(--text-default)] mb-1" for="priority-sort">Sort</label>
            <div class="relative">
              <button
                class="w-full flex items-center justify-between px-3 py-2 text-[var(--text-default)] bg-[var(--input-bg)] border border-[var(--input-border)] rounded-md focus:ring-1 focus:ring-[var(--input-focus-border)] focus:border-[var(--input-focus-border)] outline-none text-left"
                id="priority-sort-button"
              >
                <span>Alphabetical</span>
                <span class="material-icons-outlined text-lg text-[var(--icon-color)]">unfold_more</span>
              </button>
              <div
                class="absolute z-10 mt-1 w-full bg-[var(--bg-default)] border border-[var(--border-default)] rounded-md shadow-lg hidden"
                id="priority-sort-dropdown"
              >
                <a class="flex items-center px-3 py-2 hover:bg-[var(--button-hover-bg)]" data-value="Manual" href="#">
                  <span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)]">swap_vert</span> Manual
                </a>
                <a
                  class="flex items-center justify-between px-3 py-2 hover:bg-[var(--button-hover-bg)] bg-[var(--button-hover-bg)]"
                  data-value="Alphabetical"
                  href="#"
                >
                  <div class="flex items-center">
                    <span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)]">sort_by_alpha</span> Alphabetical
                  </div>
                  <span class="material-icons-outlined text-lg text-blue-500">check</span>
                </a>
                <a class="flex items-center px-3 py-2 hover:bg-[var(--button-hover-bg)]" data-value="Reverse alphabetical" href="#">
                  <span class="material-icons-outlined text-lg mr-2 text-[var(--icon-color)] रొate-180">sort_by_alpha</span> Reverse alphabetical
                </a>
              </div>
            </div>
          </div>
          <div class="mb-6">
            <label class="block text-sm font-medium text-[var(--text-default)] mb-1">Colors</label>
            <div class="grid grid-cols-5 gap-2">
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(248, 248, 247)"></div>
                <span class="text-sm text-[var(--tag-gray-text)]">Default</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(248, 248, 247)"></div>
                <span class="text-sm text-[var(--tag-gray-text)]">Gray</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(244, 238, 238)"></div>
                <span class="text-sm" style="color: var(--tag-brown-text)">Brown</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(251, 236, 221)"></div>
                <span class="text-sm" style="color: var(--tag-orange-text)">Orange</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(251, 243, 219)"></div>
                <span class="text-sm" style="color: var(--tag-yellow-text)">Yellow</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(237, 243, 236)"></div>
                <span class="text-sm" style="color: var(--tag-green-text)">Green</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(231, 243, 248)"></div>
                <span class="text-sm" style="color: var(--tag-blue-text)">Blue</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(248, 243, 252)"></div>
                <span class="text-sm" style="color: var(--tag-purple-text)">Purple</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)] bg-[var(--button-hover-bg)]"
              >
                <div class="color-picker-dot selected" style="background-color: rgb(252, 241, 246)"></div>
                <span class="text-sm" style="color: var(--tag-pink-text)">Pink</span>
                <span class="material-icons-outlined text-lg text-blue-500 ml-auto">check</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(253, 235, 236)"></div>
                <span class="text-sm" style="color: var(--tag-red-text)">Red</span>
              </button>
            </div>
          </div>
          <div class="flex justify-start pt-4 border-t border-[var(--border-default)]">
            <button class="flex items-center px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/50 rounded-md">
              <span class="material-icons-outlined text-lg mr-1">delete</span> Delete
            </button>
          </div>
        </div>
        <div class="hidden" id="editor-work">
          <div class="mb-6">
            <label class="block text-sm font-medium text-[var(--text-default)] mb-1" for="work-name">Name</label>
            <input
              class="w-full px-3 py-2 text-[var(--text-default)] bg-[var(--input-bg)] border border-[var(--input-border)] rounded-md focus:ring-1 focus:ring-[var(--input-focus-border)] focus:border-[var(--input-focus-border)] outline-none"
              id="work-name"
              type="text"
              value="Work"
            />
          </div>
          <div class="mb-6">
            <label class="block text-sm font-medium text-[var(--text-default)] mb-1">Color</label>
            <div class="grid grid-cols-5 gap-2">
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(248, 248, 247)"></div>
                <span class="text-sm text-[var(--tag-gray-text)]">Default</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(248, 248, 247)"></div>
                <span class="text-sm text-[var(--tag-gray-text)]">Gray</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)] bg-[var(--button-hover-bg)]"
              >
                <div class="color-picker-dot selected" style="background-color: rgb(244, 238, 238)"></div>
                <span class="text-sm" style="color: var(--tag-brown-text)">Brown</span>
                <span class="material-icons-outlined text-lg text-blue-500 ml-auto">check</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(251, 236, 221)"></div>
                <span class="text-sm" style="color: var(--tag-orange-text)">Orange</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(251, 243, 219)"></div>
                <span class="text-sm" style="color: var(--tag-yellow-text)">Yellow</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(237, 243, 236)"></div>
                <span class="text-sm" style="color: var(--tag-green-text)">Green</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(231, 243, 248)"></div>
                <span class="text-sm" style="color: var(--tag-blue-text)">Blue</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(248, 243, 252)"></div>
                <span class="text-sm" style="color: var(--tag-purple-text)">Purple</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(252, 241, 246)"></div>
                <span class="text-sm" style="color: var(--tag-pink-text)">Pink</span>
              </button>
              <button
                class="flex items-center p-2 rounded-md hover:bg-[var(--button-hover-bg)] border border-transparent hover:border-[var(--border-default)]"
              >
                <div class="color-picker-dot" style="background-color: rgb(253, 235, 236)"></div>
                <span class="text-sm" style="color: var(--tag-red-text)">Red</span>
              </button>
            </div>
          </div>
          <div class="flex justify-start pt-4 border-t border-[var(--border-default)]">
            <button class="flex items-center px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/50 rounded-md">
              <span class="material-icons-outlined text-lg mr-1">delete</span> Delete
            </button>
          </div>
        </div>
      </div>
    </main>
    <script>
      const themeToggle = document.getElementById("theme-toggle");
      const body = document.body;
      // Check for saved theme preference or use system preference
      if (localStorage.getItem("theme") === "dark" || (!("theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
        body.classList.replace("light-theme", "dark-theme");
      }
      themeToggle.addEventListener("click", () => {
        if (body.classList.contains("light-theme")) {
          body.classList.replace("light-theme", "dark-theme");
          localStorage.setItem("theme", "dark");
        } else {
          body.classList.replace("dark-theme", "light-theme");
          localStorage.setItem("theme", "light");
        }
      });
      // Dropdown toggle logic
      function setupDropdown(buttonId, dropdownId) {
        const button = document.getElementById(buttonId);
        const dropdown = document.getElementById(dropdownId);
        if (!button || !dropdown) return;
        button.addEventListener("click", (event) => {
          event.stopPropagation();
          dropdown.classList.toggle("hidden");
        });
        document.addEventListener("click", (event) => {
          if (!dropdown.contains(event.target) && !button.contains(event.target)) {
            dropdown.classList.add("hidden");
          }
        });
        dropdown.querySelectorAll("a").forEach((item) => {
          item.addEventListener("click", (e) => {
            e.preventDefault();
            const selectedValue = item.dataset.value;
            button.querySelector("span:first-child").textContent = selectedValue;
            // Update checkmark
            dropdown.querySelectorAll("a").forEach((link) => link.classList.remove("bg-[var(--button-hover-bg)]"));
            dropdown.querySelectorAll("a span.material-icons-outlined.text-blue-500").forEach((icon) => icon.remove());
            item.classList.add("bg-[var(--button-hover-bg)]");
            const checkIcon = document.createElement("span");
            checkIcon.classList.add("material-icons-outlined", "text-lg", "text-blue-500");
            checkIcon.textContent = "check";
            if (item.querySelector(".flex.items-center.justify-between")) {
              // for items with existing structure
              item.appendChild(checkIcon);
            } else if (item.querySelector(".flex.items-center")) {
              item.querySelector(".flex.items-center").insertAdjacentElement("afterend", checkIcon);
            } else {
              item.appendChild(checkIcon); // Fallback
            }
            dropdown.classList.add("hidden");
          });
        });
      }
      setupDropdown("priority-type-button", "priority-type-dropdown");
      setupDropdown("priority-sort-button", "priority-sort-dropdown");
      // Navigation logic
      const navLinks = document.querySelectorAll("aside nav a");
      const editorPriority = document.getElementById("editor-priority");
      const editorWork = document.getElementById("editor-work");
      const allEditors = [editorPriority, editorWork]; // Add other editors here
      navLinks.forEach((link) => {
        link.addEventListener("click", (e) => {
          e.preventDefault();
          navLinks.forEach((l) => l.classList.remove("bg-[var(--button-hover-bg)]", "font-medium"));
          link.classList.add("bg-[var(--button-hover-bg)]", "font-medium");
          allEditors.forEach((editor) => editor.classList.add("hidden"));
          if (link.id === "nav-priority" || link.id === "nav-status" || link.id === "nav-project") {
            editorPriority.classList.remove("hidden");
            // Here you would typically load different data based on the selected field
            // For this example, 'Priority' editor is always shown for custom fields
            if (link.id === "nav-priority") {
              document.getElementById("priority-name").value = "Priority";
            } else if (link.id === "nav-status") {
              document.getElementById("priority-name").value = "Status";
            } else if (link.id === "nav-project") {
              document.getElementById("priority-name").value = "Project";
            }
          } else if (link.id === "nav-work" || link.id === "nav-personal" || link.id === "nav-learning") {
            editorWork.classList.remove("hidden");
            if (link.id === "nav-work") {
              document.getElementById("work-name").value = "Work";
            } else if (link.id === "nav-personal") {
              document.getElementById("work-name").value = "Personal";
            } else if (link.id === "nav-learning") {
              document.getElementById("work-name").value = "Learning";
            }
          }
        });
      });
      // Draggable items (simple example, for full functionality use a library like SortableJS)
      const draggableItems = document.querySelectorAll(".draggable-item");
      draggableItems.forEach((item) => {
        item.addEventListener("dragstart", () => {
          item.classList.add("opacity-50");
        });
        item.addEventListener("dragend", () => {
          item.classList.remove("opacity-50");
        });
      });
      // Color picker logic
      const colorButtons = document.querySelectorAll("#editor-priority .grid button, #editor-work .grid button");
      colorButtons.forEach((button) => {
        button.addEventListener("click", (e) => {
          e.preventDefault();
          const parentGrid = button.closest(".grid");
          parentGrid.querySelectorAll(".color-picker-dot").forEach((dot) => dot.classList.remove("selected"));
          parentGrid.querySelectorAll("button").forEach((btn) => btn.classList.remove("bg-[var(--button-hover-bg)]"));
          parentGrid.querySelectorAll("span.material-icons-outlined.text-blue-500").forEach((icon) => icon.remove());
          button.querySelector(".color-picker-dot").classList.add("selected");
          button.classList.add("bg-[var(--button-hover-bg)]");
          const checkIcon = document.createElement("span");
          checkIcon.classList.add("material-icons-outlined", "text-lg", "text-blue-500", "ml-auto");
          checkIcon.textContent = "check";
          button.appendChild(checkIcon);
        });
      });
    </script>
  </body>
</html>
