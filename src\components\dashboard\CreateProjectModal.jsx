"use client";

import { useState, useEffect } from "react";
import { X, ChevronDown } from "lucide-react";

export default function CreateProjectModal({ isOpen, onClose, onSave }) {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [availableProperties, setAvailableProperties] = useState([]);
  const [lifeAspects, setLifeAspects] = useState([]);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    life_aspect_id: "",
    customFields: {},
  });

  // Fetch data when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchAvailableProperties();
      fetchLifeAspects();
      // Reset form data
      setFormData({
        name: "",
        description: "",
        life_aspect_id: "",
        customFields: {},
      });
    }
  }, [isOpen]);

  const fetchAvailableProperties = async () => {
    try {
      const response = await fetch("/api/properties/for-view", {
        credentials: "include",
      });

      if (response.ok) {
        const properties = await response.json();
        setAvailableProperties(properties);
      }
    } catch (error) {
      console.error("Error fetching properties:", error);
    }
  };

  const fetchLifeAspects = async () => {
    try {
      const response = await fetch("/api/life-aspects", {
        credentials: "include",
      });

      if (response.ok) {
        const aspects = await response.json();
        setLifeAspects(aspects);
      }
    } catch (error) {
      console.error("Error fetching life aspects:", error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCustomFieldChange = (fieldId, value) => {
    setFormData((prev) => ({
      ...prev,
      customFields: {
        ...prev.customFields,
        [fieldId]: value,
      },
    }));
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      alert("Project name is required");
      return;
    }

    setSaving(true);
    try {
      const response = await fetch("/api/projects", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        onSave();
        onClose();
      } else {
        const errorData = await response.json();
        alert(errorData.error || "Failed to create project");
      }
    } catch (error) {
      console.error("Error creating project:", error);
      alert("Failed to create project");
    } finally {
      setSaving(false);
    }
  };

  const renderFieldInput = (property) => {
    const currentValue = formData.customFields[property.id];

    switch (property.field_type) {
      case "Select":
        const selectedOptionId = currentValue?.selectedOptions?.[0]?.id || currentValue?.selectedOptions?.[0] || "";

        return (
          <div className="relative">
            <select
              value={selectedOptionId}
              onChange={(e) =>
                handleCustomFieldChange(property.id, {
                  selectedOptions: e.target.value ? [{ id: e.target.value }] : [],
                })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
            >
              <option value="">Select an option</option>
              {property.choice_options?.map((option) => (
                <option key={option.id} value={option.id}>
                  {option.value}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>
        );

      case "Text":
        return (
          <input
            type="text"
            value={currentValue?.text_value || ""}
            onChange={(e) => handleCustomFieldChange(property.id, { text_value: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder={`Enter ${property.name.toLowerCase()}`}
          />
        );

      case "Number":
        return (
          <input
            type="number"
            value={currentValue?.text_value || ""}
            onChange={(e) => handleCustomFieldChange(property.id, { text_value: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder={`Enter ${property.name.toLowerCase()}`}
          />
        );

      case "Date":
        return (
          <input
            type="date"
            value={currentValue?.text_value || ""}
            onChange={(e) => handleCustomFieldChange(property.id, { text_value: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        );

      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Create New Project</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="space-y-6">
            {/* Basic Fields */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Project Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter project name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter project description"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Life Aspect</label>
                <div className="relative">
                  <select
                    value={formData.life_aspect_id}
                    onChange={(e) => handleInputChange("life_aspect_id", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
                  >
                    <option value="">Select a life aspect</option>
                    {lifeAspects.map((aspect) => (
                      <option key={aspect.id} value={aspect.id}>
                        {aspect.name}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>

            {/* Custom Fields */}
            {availableProperties.length > 0 && (
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">Custom Properties</h3>
                <div className="space-y-4">
                  {availableProperties.map((property) => (
                    <div key={property.id}>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {property.name}
                        {property.is_required && <span className="text-red-500 ml-1">*</span>}
                      </label>
                      {renderFieldInput(property)}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            disabled={saving}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={saving || !formData.name.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {saving ? "Creating..." : "Create Project"}
          </button>
        </div>
      </div>
    </div>
  );
}
