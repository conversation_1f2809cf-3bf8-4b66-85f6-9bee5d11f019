"use client";

import { useState } from "react";
import { Plus, GripVertical } from "lucide-react";
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

// Sortable item component
function SortableItem({ id, children, isActive, onClick }) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex items-center space-x-2 px-2 py-1.5 text-sm rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer ${
        isActive ? "bg-gray-200 dark:bg-gray-700 font-medium" : ""
      }`}
      onClick={onClick}
    >
      <div {...attributes} {...listeners} className="cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600">
        <GripVertical size={16} />
      </div>
      {children}
    </div>
  );
}

export default function PropertiesSidebar({
  properties,
  lifeAspects,
  activeSection,
  activeItem,
  onSectionChange,
  onItemCreate,
  onPropertiesReorder,
  onLifeAspectsReorder,
}) {
  const [isDragging, setIsDragging] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDragEnd = async (event) => {
    setIsDragging(false);
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    // Determine if we're reordering properties or life aspects
    const isProperty = properties.some((p) => p.id === active.id);
    const items = isProperty ? properties : lifeAspects;
    const setItems = isProperty ? onPropertiesReorder : onLifeAspectsReorder;
    const endpoint = isProperty ? "/api/properties/reorder" : "/api/life-aspects/reorder";

    const oldIndex = items.findIndex((item) => item.id === active.id);
    const newIndex = items.findIndex((item) => item.id === over.id);

    const newItems = arrayMove(items, oldIndex, newIndex);
    setItems(newItems);

    // Persist to database
    try {
      await fetch(endpoint, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ids: newItems.map((item) => item.id) }),
        credentials: "include",
      });
    } catch (error) {
      console.error("Error reordering items:", error);
      // Revert on error
      setItems(items);
    }
  };

  return (
    <aside className="w-64 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-4 overflow-y-auto">
      <div className="space-y-6">
        {/* Properties Section */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">Properties</h3>
            <button
              onClick={() => onItemCreate("property")}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Add Property"
            >
              <Plus size={16} />
            </button>
          </div>

          <DndContext sensors={sensors} collisionDetection={closestCenter} onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
            <SortableContext items={properties.map((p) => p.id)} strategy={verticalListSortingStrategy}>
              <nav className="space-y-1">
                {properties.map((property) => (
                  <SortableItem
                    key={property.id}
                    id={property.id}
                    isActive={activeSection === "properties" && activeItem?.id === property.id}
                    onClick={() => onSectionChange("properties", property)}
                  >
                    <span className="flex-1 truncate text-gray-700 dark:text-gray-300">{property.name}</span>
                  </SortableItem>
                ))}
              </nav>
            </SortableContext>
          </DndContext>

          {properties.length === 0 && <p className="text-xs text-gray-500 dark:text-gray-400 italic">No properties yet</p>}
        </div>

        {/* Life Aspects Section */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">Life Aspects</h3>
            <button
              onClick={() => onItemCreate("life-aspect")}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Add Life Aspect"
            >
              <Plus size={16} />
            </button>
          </div>

          <DndContext sensors={sensors} collisionDetection={closestCenter} onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
            <SortableContext items={lifeAspects.map((la) => la.id)} strategy={verticalListSortingStrategy}>
              <nav className="space-y-1">
                {lifeAspects.map((aspect) => (
                  <SortableItem
                    key={aspect.id}
                    id={aspect.id}
                    isActive={activeSection === "life-aspects" && activeItem?.id === aspect.id}
                    onClick={() => onSectionChange("life-aspects", aspect)}
                  >
                    <div className="w-4 h-4 rounded-full border border-gray-300" style={{ backgroundColor: aspect.color }} />
                    <span className="flex-1 truncate text-gray-700 dark:text-gray-300">{aspect.name}</span>
                  </SortableItem>
                ))}
              </nav>
            </SortableContext>
          </DndContext>

          {lifeAspects.length === 0 && <p className="text-xs text-gray-500 dark:text-gray-400 italic">No life aspects yet</p>}
        </div>
      </div>
    </aside>
  );
}
