import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - Fetch properties formatted for view dropdowns
export async function GET(request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;

    // Fetch all properties with their choice options
    const properties = await prisma.workitems_custom_field_definition.findMany({
      where: {
        user_id: userId,
      },
      include: {
        choice_options: {
          orderBy: { sort_order: "asc" },
        },
      },
      orderBy: { sort_order: "asc" },
    });

    // Format properties for dropdown usage
    const formattedProperties = properties.map((property) => ({
      id: property.id,
      name: property.name,
      field_type: property.field_type,
      choice_options: property.choice_options.map((option) => ({
        id: option.id,
        value: option.value,
        color: option.color,
        sort_order: option.sort_order,
        is_default: option.is_default,
      })),
    }));

    // Add special "Life Aspect" option
    const viewOptions = [
      {
        id: "lifeAspect",
        name: "Life Aspect",
        field_type: "Select",
        choice_options: [], // Life aspects will be fetched separately when needed
      },
      ...formattedProperties,
    ];

    return NextResponse.json(viewOptions);
  } catch (error) {
    console.error("Error fetching properties for view:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
