import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// PUT - Reorder properties
export async function PUT(request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { ids } = await request.json();

    if (!Array.isArray(ids)) {
      return NextResponse.json({ error: "IDs must be an array" }, { status: 400 });
    }

    // Verify all properties belong to the user
    const properties = await prisma.workitems_custom_field_definition.findMany({
      where: {
        id: { in: ids },
        user_id: session.user.id,
      },
    });

    if (properties.length !== ids.length) {
      return NextResponse.json({ error: "Some properties not found or unauthorized" }, { status: 404 });
    }

    // Update sort orders
    const updatePromises = ids.map((id, index) =>
      prisma.workitems_custom_field_definition.update({
        where: { id },
        data: { sort_order: index },
      })
    );

    await Promise.all(updatePromises);

    return NextResponse.json({ message: "Properties reordered successfully" });
  } catch (error) {
    console.error("Error reordering properties:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
