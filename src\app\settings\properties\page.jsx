"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import PropertiesSidebar from "./components/PropertiesSidebar";
import PropertyEditor from "./components/PropertyEditor";
import LifeAspectEditor from "./components/LifeAspectEditor";

export default function PropertiesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeSection, setActiveSection] = useState("properties");
  const [activeItem, setActiveItem] = useState(null);
  const [properties, setProperties] = useState([]);
  const [lifeAspects, setLifeAspects] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch data from API
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch properties (custom field definitions)
      const propertiesResponse = await fetch("/api/properties", {
        credentials: "include",
      });
      if (propertiesResponse.ok) {
        const propertiesData = await propertiesResponse.json();
        setProperties(propertiesData);

        // Set first property as active if none selected
        if (propertiesData.length > 0 && !activeItem) {
          setActiveItem(propertiesData[0]);
          setActiveSection("properties");
        }
      }

      // Fetch life aspects
      const lifeAspectsResponse = await fetch("/api/settings/life-aspects", {
        credentials: "include",
      });
      if (lifeAspectsResponse.ok) {
        const lifeAspectsData = await lifeAspectsResponse.json();
        setLifeAspects(lifeAspectsData);

        // If no properties but life aspects exist, set first life aspect as active
        if (properties.length === 0 && lifeAspectsData.length > 0 && !activeItem) {
          setActiveItem(lifeAspectsData[0]);
          setActiveSection("life-aspects");
        }
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  }, [activeItem, properties.length]);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
      return;
    }

    if (status === "authenticated") {
      fetchData();
    }
  }, [status, router, fetchData]);

  const handleSectionChange = (section, item) => {
    setActiveSection(section);
    setActiveItem(item);
  };

  const handleItemCreate = async (type) => {
    try {
      const endpoint = type === "property" ? "/api/properties" : "/api/settings/life-aspects";
      const defaultName = type === "property" ? "New Property" : "New Life Aspect";

      const response = await fetch(endpoint, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: defaultName,
          ...(type === "property" ? { field_type: "Select" } : { color: "#3498db" }),
        }),
        credentials: "include",
      });

      if (response.ok) {
        const newItem = await response.json();

        if (type === "property") {
          setProperties((prev) => [...prev, newItem]);
          setActiveSection("properties");
        } else {
          setLifeAspects((prev) => [...prev, newItem]);
          setActiveSection("life-aspects");
        }

        setActiveItem(newItem);
      }
    } catch (error) {
      console.error(`Error creating ${type}:`, error);
    }
  };

  const handleItemUpdate = (updatedItem) => {
    if (activeSection === "properties") {
      setProperties((prev) => prev.map((item) => (item.id === updatedItem.id ? updatedItem : item)));
    } else {
      setLifeAspects((prev) => prev.map((item) => (item.id === updatedItem.id ? updatedItem : item)));
    }
    setActiveItem(updatedItem);
  };

  const handleItemDelete = (deletedItemId) => {
    if (activeSection === "properties") {
      const updatedProperties = properties.filter((item) => item.id !== deletedItemId);
      setProperties(updatedProperties);

      // Set next active item
      if (updatedProperties.length > 0) {
        setActiveItem(updatedProperties[0]);
      } else if (lifeAspects.length > 0) {
        setActiveSection("life-aspects");
        setActiveItem(lifeAspects[0]);
      } else {
        setActiveItem(null);
      }
    } else {
      const updatedLifeAspects = lifeAspects.filter((item) => item.id !== deletedItemId);
      setLifeAspects(updatedLifeAspects);

      // Set next active item
      if (updatedLifeAspects.length > 0) {
        setActiveItem(updatedLifeAspects[0]);
      } else if (properties.length > 0) {
        setActiveSection("properties");
        setActiveItem(properties[0]);
      } else {
        setActiveItem(null);
      }
    }
  };

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="text-gray-600 dark:text-gray-400">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 flex">
      <PropertiesSidebar
        properties={properties}
        lifeAspects={lifeAspects}
        activeSection={activeSection}
        activeItem={activeItem}
        onSectionChange={handleSectionChange}
        onItemCreate={handleItemCreate}
        onPropertiesReorder={setProperties}
        onLifeAspectsReorder={setLifeAspects}
      />

      <main className="flex-1 p-8 overflow-y-auto">
        <div className="max-w-2xl mx-auto">
          {activeSection === "properties" && activeItem && (
            <PropertyEditor property={activeItem} onUpdate={handleItemUpdate} onDelete={handleItemDelete} />
          )}

          {activeSection === "life-aspects" && activeItem && (
            <LifeAspectEditor aspect={activeItem} onUpdate={handleItemUpdate} onDelete={handleItemDelete} />
          )}

          {!activeItem && (
            <div className="text-center text-gray-500 dark:text-gray-400 mt-20">
              <p className="text-lg mb-4">No properties or life aspects found</p>
              <p className="text-sm">Create your first property or life aspect using the sidebar</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
