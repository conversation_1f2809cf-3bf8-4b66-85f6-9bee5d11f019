"use client";

import { useState } from "react";
import { format, addDays, subDays, startOfWeek, endOfWeek, getISOWeek, isToday, isSameWeek } from "date-fns";
import { ChevronLeft, ChevronRight, Calendar, Home } from "lucide-react";

export default function WeekNavigator() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [weekStartsOn, setWeekStartsOn] = useState(0); // 0 = Sunday, 1 = Monday
  const [showCalendar, setShowCalendar] = useState(false);
  
  const today = new Date();
  const start = startOfWeek(currentDate, { weekStartsOn });
  const end = endOfWeek(currentDate, { weekStartsOn });
  
  const goToPreviousWeek = () => {
    setCurrentDate(subDays(currentDate, 7));
  };
  
  const goToNextWeek = () => {
    setCurrentDate(addDays(currentDate, 7));
  };
  
  const goToToday = () => {
    setCurrentDate(new Date());
  };
  
  const isCurrentWeek = isSameWeek(currentDate, today, { weekStartsOn });
  
  // Generate calendar grid for current month
  const generateCalendarDays = () => {
    const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    const calendarStart = startOfWeek(monthStart, { weekStartsOn });
    const calendarEnd = endOfWeek(monthEnd, { weekStartsOn });
    
    const days = [];
    let day = calendarStart;
    
    while (day <= calendarEnd) {
      days.push(new Date(day));
      day = addDays(day, 1);
    }
    
    return days;
  };
  
  const selectWeek = (date) => {
    setCurrentDate(date);
    setShowCalendar(false);
  };
  
  const calendarDays = generateCalendarDays();
  const weekDays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
  if (weekStartsOn === 1) {
    weekDays.push(weekDays.shift()); // Move Sunday to end if week starts on Monday
  }

  return (
    <div className="flex flex-col items-center mb-6 relative">
      {/* Main Week Display */}
      <div className="flex items-center space-x-4 mb-2">
        <button 
          onClick={goToPreviousWeek} 
          className="p-2 rounded-full hover:bg-gray-200 transition-colors"
          title="Previous Week"
        >
          <ChevronLeft className="w-5 h-5 text-gray-800" />
        </button>
        
        <div className="text-center">
          <h1 className="text-xl font-semibold text-gray-800">
            W{getISOWeek(currentDate)}: {format(start, "dd MMM")} - {format(end, "dd MMM")}
          </h1>
          <div className="text-sm text-gray-600 mt-1">
            {format(today, "EEE, dd MMM, yyyy")}
          </div>
        </div>
        
        <button 
          onClick={goToNextWeek} 
          className="p-2 rounded-full hover:bg-gray-200 transition-colors"
          title="Next Week"
        >
          <ChevronRight className="w-5 h-5 text-gray-800" />
        </button>
      </div>
      
      {/* Controls Row */}
      <div className="flex items-center space-x-4 text-xs text-gray-500">
        {/* Week Start Selector */}
        <div className="flex items-center space-x-2">
          <span>Week starts on:</span>
          <button 
            onClick={() => setWeekStartsOn(1)} 
            className={`font-medium px-2 py-1 rounded transition-colors ${
              weekStartsOn === 1 ? "text-blue-600 bg-blue-50" : "hover:bg-gray-100"
            }`}
          >
            Mon
          </button>
          <span>|</span>
          <button 
            onClick={() => setWeekStartsOn(0)} 
            className={`font-medium px-2 py-1 rounded transition-colors ${
              weekStartsOn === 0 ? "text-blue-600 bg-blue-50" : "hover:bg-gray-100"
            }`}
          >
            Sun
          </button>
        </div>
        
        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowCalendar(!showCalendar)}
            className="flex items-center space-x-1 px-3 py-1 rounded bg-gray-100 hover:bg-gray-200 transition-colors"
            title="Open Calendar"
          >
            <Calendar className="w-3 h-3" />
            <span>Calendar</span>
          </button>
          
          {!isCurrentWeek && (
            <button
              onClick={goToToday}
              className="flex items-center space-x-1 px-3 py-1 rounded bg-blue-100 hover:bg-blue-200 text-blue-700 transition-colors"
              title="Go to Current Week"
            >
              <Home className="w-3 h-3" />
              <span>Today</span>
            </button>
          )}
        </div>
      </div>
      
      {/* Mini Calendar Dropdown */}
      {showCalendar && (
        <div className="absolute top-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 min-w-[280px]">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-800">
              {format(currentDate, "MMMM yyyy")}
            </h3>
            <button
              onClick={() => setShowCalendar(false)}
              className="p-1 rounded hover:bg-gray-100"
              title="Close Calendar"
            >
              <span className="text-gray-500">×</span>
            </button>
          </div>
          
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {weekDays.map((day, index) => (
              <div key={index} className="text-center text-xs font-medium text-gray-500 p-2">
                {day}
              </div>
            ))}
          </div>
          
          <div className="grid grid-cols-7 gap-1">
            {calendarDays.map((day, index) => {
              const isCurrentMonth = day.getMonth() === currentDate.getMonth();
              const isSelectedWeek = isSameWeek(day, currentDate, { weekStartsOn });
              const isTodayDate = isToday(day);
              
              return (
                <button
                  key={index}
                  onClick={() => selectWeek(day)}
                  className={`p-2 text-xs rounded transition-colors ${
                    isSelectedWeek
                      ? "bg-blue-500 text-white"
                      : isTodayDate
                      ? "bg-blue-100 text-blue-700 font-semibold"
                      : isCurrentMonth
                      ? "hover:bg-gray-100 text-gray-700"
                      : "text-gray-400 hover:bg-gray-50"
                  }`}
                  title={`Select week of ${format(day, "MMM dd")}`}
                >
                  {format(day, "d")}
                </button>
              );
            })}
          </div>
          
          <div className="mt-3 pt-3 border-t border-gray-200 text-xs text-gray-500 text-center">
            Click any date to select that week
          </div>
        </div>
      )}
    </div>
  );
}
