"use client";

import { useState, useEffect } from "react";
import { Trash2, Plus, GripVertical, Star } from "lucide-react";
import ColorPicker, { colorOptions } from "./ColorPicker";
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useTheme } from "next-themes";

// Sortable Value Item Component
function SortableValueItem({ value, onUpdate, onDelete, isDragEnabled = true }) {
  const [isEditing, setIsEditing] = useState(false);
  const [name, setName] = useState(value.value || "");
  const [selectedColor, setSelectedColor] = useState(value.color || "blue");
  const { theme } = useTheme();

  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: value.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  useEffect(() => {
    setName(value.value || "");
    setSelectedColor(value.color || "blue");
  }, [value]);

  const handleNameSave = async () => {
    if (name.trim() !== value.value) {
      await onUpdate(value.id, { value: name.trim() });
    }
    setIsEditing(false);
  };

  const handleColorChange = async (newColor) => {
    console.log("🎨 Color change requested:", { valueId: value.id, oldColor: selectedColor, newColor });
    setSelectedColor(newColor);
    await onUpdate(value.id, { color: newColor });
  };

  const handleDefaultToggle = async () => {
    const newDefaultState = !value.is_default;
    await onUpdate(value.id, { is_default: newDefaultState });
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      handleNameSave();
    } else if (e.key === "Escape") {
      setName(value.value || "");
      setIsEditing(false);
    }
  };

  const colorOption = colorOptions.find((c) => c.value === selectedColor) || colorOptions.find((c) => c.value === "blue");
  const colorStyle = theme === "dark" ? colorOption.dark : colorOption.light;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
    >
      {isDragEnabled ? (
        <div {...attributes} {...listeners} className="cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600">
          <GripVertical size={16} />
        </div>
      ) : (
        <div className="w-4 h-4 text-gray-300">
          <GripVertical size={16} />
        </div>
      )}

      <div className="w-6 h-6 rounded-full border border-gray-300" style={{ backgroundColor: colorStyle.bg }} />

      {isEditing ? (
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          onBlur={handleNameSave}
          onKeyDown={handleKeyPress}
          className="flex-1 px-2 py-1 text-sm bg-transparent border-none focus:ring-0 outline-none text-gray-900 dark:text-white"
          autoFocus
        />
      ) : (
        <div className="flex-1 px-2 py-1 text-sm text-gray-900 dark:text-white cursor-pointer" onClick={() => setIsEditing(true)}>
          {name}
          {value.is_default && <span className="ml-2 text-xs text-yellow-600 dark:text-yellow-400 font-medium">(default)</span>}
        </div>
      )}

      <ColorPicker selectedColor={selectedColor} onColorChange={handleColorChange} className="ml-auto" />

      <button
        onClick={handleDefaultToggle}
        className={`p-1 transition-colors ${value.is_default ? "text-yellow-500 hover:text-yellow-600" : "text-gray-400 hover:text-yellow-500"}`}
        title={value.is_default ? "Remove as default" : "Set as default"}
      >
        <Star size={16} fill={value.is_default ? "currentColor" : "none"} />
      </button>

      <button onClick={() => onDelete(value.id)} className="text-gray-400 hover:text-red-500 p-1" title="Delete value">
        <Trash2 size={16} />
      </button>
    </div>
  );
}

export default function PropertyEditor({ property, onUpdate, onDelete }) {
  const [name, setName] = useState("");
  const [fieldType, setFieldType] = useState("Select");
  const [values, setValues] = useState([]);
  const [sortPreference, setSortPreference] = useState("manual");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    if (property) {
      setName(property.name);
      setFieldType(property.field_type || "Select");
      setSortPreference(property.sort_preference || "manual");
      fetchValues();
    }
  }, [property]);

  const fetchValues = async () => {
    if (!property) return;

    try {
      const response = await fetch(`/api/properties/${property.id}/values`, {
        credentials: "include",
      });
      if (response.ok) {
        const valuesData = await response.json();
        const sortedValues = sortValues(valuesData, property.sort_preference || "manual");
        setValues(sortedValues);
      }
    } catch (error) {
      console.error("Error fetching values:", error);
    }
  };

  const sortValues = (valuesToSort, preference) => {
    const sorted = [...valuesToSort];
    switch (preference) {
      case "alpha":
        return sorted.sort((a, b) => a.value.localeCompare(b.value));
      case "rev-alpha":
        return sorted.sort((a, b) => b.value.localeCompare(a.value));
      case "manual":
      default:
        return sorted.sort((a, b) => a.sort_order - b.sort_order);
    }
  };

  const handleSortPreferenceChange = async (newPreference) => {
    if (newPreference !== sortPreference && property) {
      try {
        const response = await fetch(`/api/properties/${property.id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ sort_preference: newPreference }),
          credentials: "include",
        });

        if (response.ok) {
          const updatedProperty = await response.json();
          onUpdate(updatedProperty);
          setSortPreference(newPreference);

          // Re-sort the current values based on the new preference
          const sortedValues = sortValues(values, newPreference);
          setValues(sortedValues);
        }
      } catch (error) {
        console.error("Error updating sort preference:", error);
      }
    }
  };

  const handleNameChange = async (newName) => {
    if (newName.trim() !== property.name && property) {
      try {
        const response = await fetch(`/api/properties/${property.id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ name: newName.trim() }),
          credentials: "include",
        });

        if (response.ok) {
          const updatedProperty = await response.json();
          onUpdate(updatedProperty);
        }
      } catch (error) {
        console.error("Error updating property name:", error);
      }
    }
  };

  const handleTypeChange = async (newType) => {
    if (newType !== property.field_type && property) {
      try {
        const response = await fetch(`/api/properties/${property.id}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ field_type: newType }),
          credentials: "include",
        });

        if (response.ok) {
          const updatedProperty = await response.json();
          onUpdate(updatedProperty);
          setFieldType(newType);
        }
      } catch (error) {
        console.error("Error updating property type:", error);
      }
    }
  };

  const handleAddValue = async () => {
    try {
      const response = await fetch(`/api/properties/${property.id}/values`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          value: `Option ${values.length + 1}`,
          color: "blue",
        }),
      });

      if (response.ok) {
        const newValue = await response.json();
        setValues((prev) => [...prev, newValue]);
      }
    } catch (error) {
      console.error("Error adding value:", error);
    }
  };

  const handleValueUpdate = async (valueId, updates) => {
    console.log("🔄 Value update requested:", { valueId, updates });
    try {
      const response = await fetch(`/api/values/${valueId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updates),
        credentials: "include",
      });

      if (response.ok) {
        const updatedValue = await response.json();
        console.log("✅ Value updated successfully:", updatedValue);
        setValues((prev) => prev.map((v) => (v.id === valueId ? updatedValue : v)));
      } else {
        console.error("❌ Value update failed:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("Error updating value:", error);
    }
  };

  const handleValueDelete = async (valueId) => {
    try {
      const response = await fetch(`/api/values/${valueId}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (response.ok) {
        setValues((prev) => prev.filter((v) => v.id !== valueId));
      }
    } catch (error) {
      console.error("Error deleting value:", error);
    }
  };

  const handleValuesReorder = async (event) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = values.findIndex((v) => v.id === active.id);
    const newIndex = values.findIndex((v) => v.id === over.id);

    const originalValues = [...values]; // Store original state for revert
    const newValues = arrayMove(values, oldIndex, newIndex);
    setValues(newValues);

    // Persist to database
    try {
      const response = await fetch("/api/values/reorder", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ids: newValues.map((v) => v.id) }),
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log("Values reordered successfully");
    } catch (error) {
      console.error("Error reordering values:", error);
      // Revert on error
      setValues(originalValues);
    }
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/properties/${property.id}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (response.ok) {
        onDelete(property.id);
      }
    } catch (error) {
      console.error("Error deleting property:", error);
    }
    setShowDeleteConfirm(false);
  };

  if (!property) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400 mt-20">
        <p>Select a property to edit</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Property Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name</label>
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          onBlur={() => handleNameChange(name)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Property Type */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type</label>
        <select
          value={fieldType}
          onChange={(e) => handleTypeChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="Select">Select</option>
          <option value="MultiSelect">Multi-Select</option>
          <option value="Text">Text</option>
          <option value="Number">Number</option>
          <option value="Date">Date</option>
        </select>
      </div>

      {/* Values (for Select and MultiSelect types) */}
      {(fieldType === "Select" || fieldType === "MultiSelect") && (
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Options</label>
            <button
              onClick={handleAddValue}
              className="flex items-center space-x-1 px-3 py-1 text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            >
              <Plus size={16} />
              <span>Add an option</span>
            </button>
          </div>

          {/* Sort Dropdown */}
          <div className="mb-3">
            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Sort</label>
            <select
              value={sortPreference}
              onChange={(e) => handleSortPreferenceChange(e.target.value)}
              className="w-32 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="manual">Manual</option>
              <option value="alpha">Alphabetical</option>
              <option value="rev-alpha">Reverse Alphabetical</option>
            </select>
          </div>

          {sortPreference === "manual" ? (
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleValuesReorder}>
              <SortableContext items={values.map((v) => v.id)} strategy={verticalListSortingStrategy}>
                <div className="space-y-2">
                  {values.map((value) => (
                    <SortableValueItem key={value.id} value={value} onUpdate={handleValueUpdate} onDelete={handleValueDelete} isDragEnabled={true} />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          ) : (
            <div className="space-y-2">
              {values.map((value) => (
                <SortableValueItem key={value.id} value={value} onUpdate={handleValueUpdate} onDelete={handleValueDelete} isDragEnabled={false} />
              ))}
            </div>
          )}

          {values.length === 0 && <p className="text-sm text-gray-500 dark:text-gray-400 italic">No options yet. Add your first option above.</p>}
        </div>
      )}

      {/* Delete Property */}
      <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setShowDeleteConfirm(true)}
          className="flex items-center space-x-2 px-4 py-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md"
        >
          <Trash2 size={16} />
          <span>Delete</span>
        </button>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Delete Property</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">Are you sure you want to delete "{property.name}"? This action cannot be undone.</p>
            <div className="flex space-x-3">
              <button onClick={handleDelete} className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                Delete
              </button>
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
