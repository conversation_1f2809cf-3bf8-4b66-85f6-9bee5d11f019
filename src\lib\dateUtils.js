import { format } from 'date-fns';

/**
 * Formats a date to the requested format: "Mon, 30-Jul, 25"
 * @param {Date|string} date - The date to format
 * @returns {string} - Formatted date string
 */
export function formatDateDisplay(date) {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }
  
  // Format: "Mon, 30-Jul, 25"
  return format(dateObj, 'EEE, dd-MMM, yy');
}

/**
 * Formats a date for HTML input[type="date"] (YYYY-MM-DD)
 * @param {Date|string} date - The date to format
 * @returns {string} - Formatted date string for input
 */
export function formatDateForInput(date) {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  return format(dateObj, 'yyyy-MM-dd');
}

/**
 * Parses a date from HTML input[type="date"] format
 * @param {string} dateString - The date string from input (YYYY-MM-DD)
 * @returns {Date|null} - Parsed date object or null if invalid
 */
export function parseDateFromInput(dateString) {
  if (!dateString) return null;
  
  const date = new Date(dateString + 'T00:00:00');
  return isNaN(date.getTime()) ? null : date;
}
