@import "tailwindcss";

@variant dark (.dark &);
/* :root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

/* @theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
} */
/* 
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
} */

/* Custom styles for drag-and-drop behavior */
/* 
.sortable-ghost {
  opacity: 0.4;
  background-color: #cce7ff;
}

.sortable-chosen {
  cursor: grabbing;
}

.item-card,
.l0-drag-handle,
.row-drag-handle {
  cursor: grab;
}

.row-drag-handle,
.l0-drag-handle {
  cursor: move;
} */

/* Style for editable text */

/*
.editable-text:hover {
  background-color: #f0f8ff; 
  outline: 1px dashed #a0d8f0;
}

[contenteditable="true"] {
  outline: 2px solid #3b82f6;
  background-color: white;
  cursor: text;
}
  */
