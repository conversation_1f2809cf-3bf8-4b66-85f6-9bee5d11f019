import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// POST - Get projects in matrix view format
export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { rowPropertyId, columnPropertyId } = await request.json();

    if (!rowPropertyId || !columnPropertyId) {
      return NextResponse.json({ error: "Both rowPropertyId and columnPropertyId are required" }, { status: 400 });
    }

    const userId = session.user.id;

    // Fetch all projects for the user with their relationships
    const projects = await prisma.workitems_project.findMany({
      where: {
        user_id: userId,
      },
      include: {
        life_aspect: true,
        custom_field_values: {
          include: {
            field_definition: true,
            selected_options: true,
          },
        },
        parent_project: true,
        sub_projects: {
          include: {
            life_aspect: true,
            custom_field_values: {
              include: {
                field_definition: true,
                selected_options: true,
              },
            },
          },
        },
      },
      orderBy: { sort_order: "asc" },
    });

    // Get row and column property definitions and their options
    const [rowProperty, columnProperty] = await Promise.all([
      rowPropertyId === "lifeAspect"
        ? null
        : prisma.workitems_custom_field_definition.findFirst({
            where: { id: rowPropertyId, user_id: userId },
            include: { choice_options: { orderBy: { sort_order: "asc" } } },
          }),
      columnPropertyId === "lifeAspect"
        ? null
        : prisma.workitems_custom_field_definition.findFirst({
            where: { id: columnPropertyId, user_id: userId },
            include: { choice_options: { orderBy: { sort_order: "asc" } } },
          }),
    ]);

    // Get life aspects if rowPropertyId or columnPropertyId is "lifeAspect"
    const lifeAspects =
      rowPropertyId === "lifeAspect" || columnPropertyId === "lifeAspect"
        ? await prisma.workitems_life_aspect.findMany({
            where: { user_id: userId },
            orderBy: { sort_order: "asc" },
          })
        : [];

    if (columnPropertyId !== "lifeAspect" && !columnProperty) {
      return NextResponse.json({ error: "Column property not found" }, { status: 404 });
    }

    if (rowPropertyId !== "lifeAspect" && !rowProperty) {
      return NextResponse.json({ error: "Row property not found" }, { status: 404 });
    }

    // Build the response structure
    const response = await buildMatrixResponse({
      projects,
      rowPropertyId,
      columnPropertyId,
      rowProperty,
      columnProperty,
      lifeAspects,
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching matrix view:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Helper function to build the matrix response
async function buildMatrixResponse({ projects, rowPropertyId, columnPropertyId, rowProperty, columnProperty, lifeAspects }) {
  // Build rows array
  const rows = [];
  if (rowPropertyId === "lifeAspect") {
    // Add life aspects as rows
    lifeAspects.forEach((aspect) => {
      rows.push({
        id: aspect.id,
        name: aspect.name,
        color: aspect.color,
      });
    });
    // Add uncategorized row for projects without life aspect
    rows.push({
      id: "uncategorized",
      name: "Uncategorized",
      color: "#6B7280", // Gray color
    });
  } else {
    // Add custom field options as rows
    rowProperty.choice_options.forEach((option) => {
      rows.push({
        id: option.id,
        name: option.value,
        color: option.color,
      });
    });
    // Add uncategorized row for projects without this field value
    rows.push({
      id: "uncategorized",
      name: "Uncategorized",
      color: "#6B7280", // Gray color
    });
  }

  // Build columns array
  const columns = [];
  if (columnPropertyId === "lifeAspect") {
    // Add life aspects as columns
    lifeAspects.forEach((aspect) => {
      columns.push({
        id: aspect.id,
        name: aspect.name,
        color: aspect.color,
      });
    });
    // Add uncategorized column for projects without life aspect
    columns.push({
      id: "uncategorized",
      name: "Uncategorized",
      color: "#6B7280", // Gray color
    });
  } else {
    // Add custom field options as columns
    columnProperty.choice_options.forEach((option) => {
      columns.push({
        id: option.id,
        name: option.value,
        color: option.color,
      });
    });
    // Add uncategorized column for projects without this field value
    columns.push({
      id: "uncategorized",
      name: "Uncategorized",
      color: "#6B7280", // Gray color
    });
  }

  // Build project map (flat map of all projects)
  const projectMap = {};
  const addToProjectMap = (project) => {
    projectMap[project.id] = {
      id: project.id,
      name: project.name,
      description: project.description,
      life_aspect_id: project.life_aspect_id,
      life_aspect_name: project.life_aspect?.name || null,
      life_aspect_color: project.life_aspect?.color || null,
      parent_project_id: project.parent_project_id,
      sort_order: project.sort_order,
      created_at: project.created_at,
      updated_at: project.updated_at,
      subProjectIds: project.sub_projects?.map((sub) => sub.id) || [],
      // Add custom field values for easy access
      customFieldValues: project.custom_field_values.reduce((acc, cv) => {
        acc[cv.field_definition.id] = {
          fieldName: cv.field_definition.name,
          selectedOptions: cv.selected_options.map((opt) => ({
            id: opt.id,
            value: opt.value,
            color: opt.color,
          })),
          textValue: cv.text_value,
        };
        return acc;
      }, {}),
    };

    // Recursively add sub-projects
    if (project.sub_projects) {
      project.sub_projects.forEach(addToProjectMap);
    }
  };

  projects.forEach(addToProjectMap);

  // Build matrix data
  const matrixData = {};

  // Initialize matrix structure
  rows.forEach((row) => {
    matrixData[row.id] = {};
    columns.forEach((column) => {
      matrixData[row.id][column.id] = [];
    });
  });

  // Helper function to get row value for a project
  const getRowValue = (project) => {
    if (rowPropertyId === "lifeAspect") {
      return project.life_aspect_id || "uncategorized";
    } else {
      const fieldValue = project.custom_field_values.find((cv) => cv.field_definition.id === rowPropertyId);
      if (fieldValue && fieldValue.selected_options.length > 0) {
        // For multi-select fields, we take the first option
        // In the future, this could be enhanced to handle multiple values
        return fieldValue.selected_options[0].id;
      }
      return "uncategorized";
    }
  };

  // Helper function to get column value for a project
  const getColumnValue = (project) => {
    if (columnPropertyId === "lifeAspect") {
      return project.life_aspect_id || "uncategorized";
    } else {
      const fieldValue = project.custom_field_values.find((cv) => cv.field_definition.id === columnPropertyId);
      if (fieldValue && fieldValue.selected_options.length > 0) {
        // For multi-select fields, we take the first option
        // In the future, this could be enhanced to handle multiple values
        return fieldValue.selected_options[0].id;
      }
      return "uncategorized";
    }
  };

  // Helper function to validate if row/column values exist in our defined options
  const isValidRowValue = (value) => {
    if (value === "uncategorized") return true;
    if (rowPropertyId === "lifeAspect") {
      return lifeAspects.some((aspect) => aspect.id === value);
    } else {
      return rowProperty.choice_options.some((option) => option.id === value);
    }
  };

  const isValidColumnValue = (value) => {
    if (value === "uncategorized") return true;
    if (columnPropertyId === "lifeAspect") {
      return lifeAspects.some((aspect) => aspect.id === value);
    } else {
      return columnProperty.choice_options.some((option) => option.id === value);
    }
  };

  // Place root projects (projects without parent) in the matrix
  const rootProjects = projects.filter((p) => !p.parent_project_id);

  rootProjects.forEach((project) => {
    const rowValue = getRowValue(project);
    const columnValue = getColumnValue(project);

    // Validate and fallback to uncategorized if needed
    const finalRowValue = isValidRowValue(rowValue) ? rowValue : "uncategorized";
    const finalColumnValue = isValidColumnValue(columnValue) ? columnValue : "uncategorized";

    // Ensure the row and column exist in our matrix
    if (matrixData[finalRowValue] && matrixData[finalRowValue][finalColumnValue]) {
      matrixData[finalRowValue][finalColumnValue].push(project.id);
    } else {
      // This should not happen if our matrix initialization is correct, but let's be safe
      console.warn(`Matrix position not found: row=${finalRowValue}, column=${finalColumnValue} for project ${project.id}`);
    }
  });

  return {
    rows,
    columns,
    matrixData,
    projectMap,
  };
}
