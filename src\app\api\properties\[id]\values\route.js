import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - Fetch all values for a property
export async function GET(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    // Verify the property belongs to the user
    const property = await prisma.workitems_custom_field_definition.findFirst({
      where: {
        id: id,
        user_id: session.user.id,
      },
    });

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    const values = await prisma.workitems_custom_field_choice_option.findMany({
      where: {
        field_definition_id: id,
      },
      orderBy: { sort_order: "asc" },
    });

    return NextResponse.json(values);
  } catch (error) {
    console.error("Error fetching values:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST - Create a new value for a property
export async function POST(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const { value, color, sort_order, is_default } = await request.json();

    if (!value) {
      return NextResponse.json({ error: "Value is required" }, { status: 400 });
    }

    // Verify the property belongs to the user
    const property = await prisma.workitems_custom_field_definition.findFirst({
      where: {
        id: id,
        user_id: session.user.id,
      },
    });

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Check if value already exists for this property
    const existingValue = await prisma.workitems_custom_field_choice_option.findUnique({
      where: {
        field_definition_id_value: {
          field_definition_id: id,
          value: value,
        },
      },
    });

    if (existingValue) {
      return NextResponse.json({ error: "Value already exists for this property" }, { status: 400 });
    }

    // Get the next sort order if not provided
    let nextSortOrder = sort_order;
    if (nextSortOrder === undefined) {
      const lastValue = await prisma.workitems_custom_field_choice_option.findFirst({
        where: { field_definition_id: id },
        orderBy: { sort_order: "desc" },
      });
      nextSortOrder = (lastValue?.sort_order || 0) + 1;
    }

    const newValue = await prisma.workitems_custom_field_choice_option.create({
      data: {
        field_definition_id: id,
        value,
        color: color || "#3498db",
        sort_order: nextSortOrder,
        is_default: is_default || false,
      },
    });

    return NextResponse.json(newValue, { status: 201 });
  } catch (error) {
    console.error("Error creating value:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
