import { NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'

export async function POST(request) {
  try {
    const { email, username, firstName, lastName, password } = await request.json()

    // Validate required fields
    if (!email || !username || !firstName || !lastName || !password) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await prisma.auth_user.findFirst({
      where: {
        OR: [
          { email: email },
          { username: username }
        ]
      }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email or username already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create user
    const user = await prisma.auth_user.create({
      data: {
        email,
        username,
        first_name: first<PERSON><PERSON>,
        last_name: lastN<PERSON>,
        password: hashedPassword,
        is_active: true,
        is_staff: false,
        is_superuser: false
      }
    })

    // Create default user preferences
    await prisma.auth_user_preferences.create({
      data: {
        user_id: user.id,
        preferences: {},
        week_starts_on: 'Sunday',
        enable_inheritance: true
      }
    })

    return NextResponse.json(
      { 
        message: 'User created successfully',
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.first_name,
          lastName: user.last_name
        }
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
