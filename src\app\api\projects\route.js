import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// POST - Create a new project
export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name, description, life_aspect_id, customFields } = await request.json();

    if (!name || !name.trim()) {
      return NextResponse.json({ error: "Project name is required" }, { status: 400 });
    }

    const userId = session.user.id;

    // Start a transaction to create project and custom fields
    const result = await prisma.$transaction(async (tx) => {
      // Create the project
      const newProject = await tx.workitems_project.create({
        data: {
          user_id: userId,
          name: name.trim(),
          description: description?.trim() || null,
          life_aspect_id: life_aspect_id || null,
          sort_order: 0, // Default sort order
        },
      });

      // Handle custom fields if provided
      if (customFields && Object.keys(customFields).length > 0) {
        for (const [fieldId, fieldData] of Object.entries(customFields)) {
          // Skip empty values
          if (!fieldData || (fieldData.text_value === "" && (!fieldData.selectedOptions || fieldData.selectedOptions.length === 0))) {
            continue;
          }

          // Verify the field belongs to the user
          const fieldDefinition = await tx.workitems_custom_field_definition.findFirst({
            where: {
              id: fieldId,
              user_id: userId,
            },
          });

          if (!fieldDefinition) {
            continue; // Skip invalid field IDs
          }

          // Create the custom field value
          const newValue = await tx.workitems_custom_field_value.create({
            data: {
              field_definition_id: fieldId,
              project_id: newProject.id,
              text_value: fieldData.text_value || null,
            },
          });

          // Handle selected options for Select/MultiSelect fields
          if (fieldData.selectedOptions && fieldData.selectedOptions.length > 0) {
            // Validate that the options belong to this field
            const validOptions = await tx.workitems_custom_field_choice_option.findMany({
              where: {
                field_definition_id: fieldId,
                id: {
                  in: fieldData.selectedOptions.map((option) => option.id),
                },
              },
            });

            if (validOptions.length > 0) {
              await tx.workitems_custom_field_value.update({
                where: { id: newValue.id },
                data: {
                  selected_options: {
                    connect: validOptions.map((option) => ({ id: option.id })),
                  },
                },
              });
            }
          }
        }
      }

      return newProject;
    });

    return NextResponse.json({ 
      success: true, 
      project: {
        id: result.id,
        name: result.name,
        description: result.description,
        life_aspect_id: result.life_aspect_id,
        created_at: result.created_at,
      }
    });
  } catch (error) {
    console.error("Error creating project:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
