generator client {
  provider = "prisma-client-js"
  // The custom 'output' line has been removed to use the default, reliable location.
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User and authentication model
model auth_user {
  id           String    @id @default(cuid())
  email        String    @unique
  username     String    @unique
  first_name   String
  last_name    String
  password     String
  is_active    Boolean   @default(true)
  is_staff     <PERSON><PERSON><PERSON>   @default(false)
  is_superuser Boolean   @default(false)
  date_joined  DateTime  @default(now())
  last_login   DateTime?

  // Relations to other models
  custom_field_definitions workitems_custom_field_definition[]
  life_aspects             workitems_life_aspect[]
  projects                 workitems_project[]
  outcomes                 workitems_outcome[]
  preferences              auth_user_preferences?

  @@map("auth_user")
}

// User Preferences model
model auth_user_preferences {
  id                 String    @id @default(cuid())
  user_id            String    @unique
  preferences        Json      @default("{}")
  week_starts_on     String    @default("Sunday") @db.VarChar(10)
  enable_inheritance Boolean   @default(true)
  created_at         DateTime  @default(now())
  updated_at         DateTime  @updatedAt

  user auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("auth_user_preferences")
}

// Enum for field types
enum FieldType {
  Select
  Text
  Number
  Date
  MultiSelect
}

// Defines the structure of a custom field
model workitems_custom_field_definition {
  id              String    @id @default(cuid())
  user_id         String
  name            String
  field_type      FieldType @default(Select)
  is_required     Boolean   @default(false)
  sort_order      Int       @default(0)
  sort_preference String    @default("manual") // "manual", "alpha", "rev-alpha"
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt

  user           auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)
  choice_options workitems_custom_field_choice_option[]
  values         workitems_custom_field_value[]

  @@unique([user_id, name])
  @@map("workitems_custom_field_definition")
}

// Stores the available options for SELECT type fields
model workitems_custom_field_choice_option {
  id                  String    @id @default(cuid())
  field_definition_id String
  value               String
  color               String    @default("#3498db")
  sort_order          Int       @default(0)
  is_default          Boolean   @default(false)
  created_at          DateTime  @default(now())
  updated_at          DateTime  @updatedAt

  field_definition workitems_custom_field_definition @relation(fields: [field_definition_id], references: [id], onDelete: Cascade)
  field_values workitems_custom_field_value[]

  @@unique([field_definition_id, value])
  @@map("workitems_custom_field_choice_option")
}

// High-level life areas defined by the user
model workitems_life_aspect {
  id         String    @id @default(cuid())
  user_id    String
  name       String
  color      String    @default("#3498db")
  sort_order Int       @default(0)
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt

  user     auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)
  projects workitems_project[]

  @@unique([user_id, name])
  @@map("workitems_life_aspect")
}

// Project model
model workitems_project {
  id                String    @id @default(cuid())
  user_id           String
  name              String
  description       String?
  life_aspect_id    String?
  parent_project_id String?
  sort_order        Int       @default(0)
  created_at        DateTime  @default(now())
  updated_at        DateTime  @updatedAt

  custom_field_values workitems_custom_field_value[]

  user           auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)
  life_aspect    workitems_life_aspect? @relation(fields: [life_aspect_id], references: [id], onDelete: SetNull)
  parent_project workitems_project?     @relation("ProjectHierarchy", fields: [parent_project_id], references: [id], onDelete: SetNull)
  sub_projects   workitems_project[]    @relation("ProjectHierarchy")
  outcomes       workitems_outcome[]

  @@map("workitems_project")
}

// Outcome model (child of a Project)
model workitems_outcome {
  id          String    @id @default(cuid())
  user_id     String
  name        String
  description String?
  project_id  String
  sort_order  Int       @default(0)
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt

  custom_field_values workitems_custom_field_value[]

  user    auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)
  project workitems_project @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@map("workitems_outcome")
}

// Dedicated table for storing the actual values of custom fields.
model workitems_custom_field_value {
  id String @id @default(cuid())

  field_definition_id String
  field_definition    workitems_custom_field_definition @relation(fields: [field_definition_id], references: [id], onDelete: Cascade)

  project_id String?
  project    workitems_project? @relation(fields: [project_id], references: [id], onDelete: Cascade)

  outcome_id String?
  outcome    workitems_outcome? @relation(fields: [outcome_id], references: [id], onDelete: Cascade)

  text_value String?
  selected_options workitems_custom_field_choice_option[]

  @@unique([field_definition_id, project_id])
  @@unique([field_definition_id, outcome_id])
  @@map("workitems_custom_field_value")
}
