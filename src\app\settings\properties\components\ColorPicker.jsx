'use client'

import { useState } from 'react'
import { Check } from 'lucide-react'

// Color definitions from style.md
const colorOptions = [
  {
    name: 'Default',
    value: 'default',
    light: { bg: 'rgb(248, 248, 247)', text: 'rgb(115, 114, 110)' },
    dark: { bg: 'rgb(47, 47, 47)', text: 'rgb(155, 155, 155)' }
  },
  {
    name: '<PERSON>',
    value: 'gray',
    light: { bg: 'rgb(248, 248, 247)', text: 'rgb(115, 114, 110)' },
    dark: { bg: 'rgb(47, 47, 47)', text: 'rgb(155, 155, 155)' }
  },
  {
    name: '<PERSON>',
    value: 'brown',
    light: { bg: 'rgb(244, 238, 238)', text: 'rgb(159, 107, 83)' },
    dark: { bg: 'rgb(74, 50, 40)', text: 'rgb(186, 133, 111)' }
  },
  {
    name: 'Orange',
    value: 'orange',
    light: { bg: 'rgb(251, 236, 221)', text: 'rgb(217, 115, 13)' },
    dark: { bg: 'rgb(92, 59, 35)', text: 'rgb(199, 125, 72)' }
  },
  {
    name: 'Yellow',
    value: 'yellow',
    light: { bg: 'rgb(251, 243, 219)', text: 'rgb(203, 145, 47)' },
    dark: { bg: 'rgb(86, 67, 40)', text: 'rgb(202, 152, 77)' }
  },
  {
    name: 'Green',
    value: 'green',
    light: { bg: 'rgb(237, 243, 236)', text: 'rgb(68, 131, 97)' },
    dark: { bg: 'rgb(36, 61, 48)', text: 'rgb(82, 158, 114)' }
  },
  {
    name: 'Blue',
    value: 'blue',
    light: { bg: 'rgb(231, 243, 248)', text: 'rgb(51, 126, 169)' },
    dark: { bg: 'rgb(20, 58, 78)', text: 'rgb(55, 154, 211)' }
  },
  {
    name: 'Purple',
    value: 'purple',
    light: { bg: 'rgb(248, 243, 252)', text: 'rgb(144, 101, 176)' },
    dark: { bg: 'rgb(60, 45, 73)', text: 'rgb(157, 104, 211)' }
  },
  {
    name: 'Pink',
    value: 'pink',
    light: { bg: 'rgb(252, 241, 246)', text: 'rgb(193, 76, 138)' },
    dark: { bg: 'rgb(78, 44, 60)', text: 'rgb(209, 87, 150)' }
  },
  {
    name: 'Red',
    value: 'red',
    light: { bg: 'rgb(253, 235, 236)', text: 'rgb(205, 60, 58)' },
    dark: { bg: 'rgb(82, 46, 42)', text: 'rgb(230, 91, 88)' }
  }
]

export default function ColorPicker({ selectedColor, onColorChange, className = '' }) {
  const [isOpen, setIsOpen] = useState(false)

  const selectedColorOption = colorOptions.find(c => c.value === selectedColor) || colorOptions[0]

  const handleColorSelect = (color) => {
    onColorChange(color.value)
    setIsOpen(false)
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <div
          className="w-5 h-5 rounded-full border border-gray-300"
          style={{ backgroundColor: selectedColorOption.light.bg }}
        />
        <span className="text-sm text-gray-700 dark:text-gray-300">
          {selectedColorOption.name}
        </span>
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Color palette */}
          <div className="absolute top-full left-0 mt-1 z-20 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 min-w-[280px]">
            <div className="grid grid-cols-5 gap-2">
              {colorOptions.map((color) => (
                <button
                  key={color.value}
                  onClick={() => handleColorSelect(color)}
                  className="relative flex flex-col items-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  title={color.name}
                >
                  <div
                    className="w-8 h-8 rounded-full border border-gray-300 mb-1 relative"
                    style={{ backgroundColor: color.light.bg }}
                  >
                    {selectedColor === color.value && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Check size={16} className="text-gray-600" />
                      </div>
                    )}
                  </div>
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    {color.name}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

// Export color options for use in other components
export { colorOptions }
