import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// PUT - Update a property
export async function PUT(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    const { name, field_type, is_required, sort_order, sort_preference } = await request.json();

    // Verify the property belongs to the user
    const existingProperty = await prisma.workitems_custom_field_definition.findFirst({
      where: {
        id: id,
        user_id: session.user.id,
      },
    });

    if (!existingProperty) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // If name is being changed, check for duplicates
    if (name && name !== existingProperty.name) {
      const duplicateProperty = await prisma.workitems_custom_field_definition.findUnique({
        where: {
          user_id_name: {
            user_id: session.user.id,
            name: name,
          },
        },
      });

      if (duplicateProperty) {
        return NextResponse.json({ error: "Property with this name already exists" }, { status: 400 });
      }
    }

    const updatedProperty = await prisma.workitems_custom_field_definition.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(field_type && { field_type }),
        ...(is_required !== undefined && { is_required }),
        ...(sort_order !== undefined && { sort_order }),
        ...(sort_preference && { sort_preference }),
      },
    });

    return NextResponse.json(updatedProperty);
  } catch (error) {
    console.error("Error updating property:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE - Delete a property
export async function DELETE(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Verify the property belongs to the user
    const existingProperty = await prisma.workitems_custom_field_definition.findFirst({
      where: {
        id: id,
        user_id: session.user.id,
      },
    });

    if (!existingProperty) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Delete the property (cascade will handle related records)
    await prisma.workitems_custom_field_definition.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Property deleted successfully" });
  } catch (error) {
    console.error("Error deleting property:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
