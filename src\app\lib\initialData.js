export const initialData = {
  sections: [
    {
      id: "section-1",
      title: "? Undefined - life aspect",
      color: "blue",
      projects: [
        {
          id: "project-1",
          title: "? Undefined",
          color: "purple",
          columns: {
            undefinedPriority: [{ id: "item-c1", content: "Outcome 6", icon: "outcome" }],
            urgentUplift: [
              { id: "item-d1", content: "Outcome 1", icon: "outcome" },
              { id: "item-d2", content: "Outcome 3", icon: "outcome" },
            ],
            notUrgentUplift: [],
            urgentNotUplift: [{ id: "item-f1", content: "Outcome 3", icon: "outcome" }],
          },
        },
        {
          id: "project-2",
          title: "Undefined",
          color: "purple",
          columns: {
            undefinedPriority: [],
            urgentUplift: [],
            notUrgentUplift: [],
            urgentNotUplift: [],
          },
        },
      ],
    },
    {
      id: "section-2",
      title: "🧠 Mind",
      color: "pink",
      projects: [
        {
          id: "project-3",
          title: "■ Time Management",
          color: "red",
          columns: {
            undefinedPriority: [],
            urgentUplift: [
              {
                id: "item-d3",
                content: "Weekly Planning",
                due: "Sun, 15 June",
                icon: "management",
              },
              {
                id: "item-d4",
                content: "Weekly Review",
                due: "Sat, 21 June",
                icon: "management",
              },
            ],
            notUrgentUplift: [
              {
                id: "item-e1",
                content: "Completing Planner Setup",
                due: "All Week",
              },
            ],
            urgentNotUplift: [],
          },
        },
        {
          id: "project-4",
          title: "■ Developing Habits",
          color: "red",
          columns: {
            undefinedPriority: [],
            urgentUplift: [
              {
                id: "item-d5",
                content: "Setting up the Tracker",
                due: "Sun, 15 June - Mon, 16 June",
                icon: "management",
              },
            ],
            notUrgentUplift: [
              {
                id: "item-e2",
                content: "Reading Atomic Habits",
                due: "All Week",
              },
              {
                id: "item-e3",
                content: "atleast 3 Time Meditation",
                star: true,
              },
            ],
            urgentNotUplift: [],
          },
        },
      ],
    },
    {
      id: "section-3",
      title: "💪 Body",
      color: "yellow",
      projects: [
        {
          id: "project-5",
          title: "■ Hair Care",
          color: "red",
          columns: {
            undefinedPriority: [],
            urgentUplift: [],
            notUrgentUplift: [],
            urgentNotUplift: [
              { id: "item-f2", content: "oil to Head and Bath" },
              { id: "item-f3", content: "Hair Cut" },
            ],
          },
        },
        {
          id: "project-6",
          title: "■ Reducing Weight",
          color: "red",
          columns: {
            undefinedPriority: [{ id: "item-c2", content: "No Outside Food this June", star: true }],
            urgentUplift: [],
            notUrgentUplift: [{ id: "item-e4", content: "finding best exercise for reducing Belly Size" }],
            urgentNotUplift: [],
          },
        },
      ],
    },
  ],
  topOutcomes: [
    { id: "outcome-1", content: "Outcome 2" },
    { id: "outcome-2", content: "Outcome 5" },
    { id: "outcome-3", content: "Outcome 9" },
  ],
};
